{% extends 'base_landing.html' %}

{% block title %}智慧食堂平台 - 专业校园餐饮管理解决方案{% endblock %}

{% block meta %}
    <meta name="description" content="智慧食堂平台提供专业的校园食堂管理解决方案，实现食品安全可视化、可管控、可追溯">
    <meta name="keywords" content="智慧食堂,校园餐饮,食品安全,食堂管理,财务管理">
{% endblock %}

{% block styles %}
<style nonce="{{ csp_nonce() if csp_nonce else '' }}">
    /* 智慧食堂首页样式 - Bootstrap 5.3.6 优化版 */
    /*
    CSS载入顺序说明：
    1. Bootstrap 5.3.6 CSS (基础框架)
    2. Font Awesome (图标)
    3. 第三方插件CSS
    4. 本地基础样式
    5. Landing页面基础样式 (base_landing.html)
    6. 页面特定样式 (当前文件) - 最高优先级

    注意：基础变量已在base_landing.html中定义，避免重复定义
    */

    /* 页面特定变量扩展 */
    :root {
        --hero-gradient-alt: linear-gradient(45deg, #667eea 0%, #764ba2 100%);
        --theme-primary: var(--bs-primary);
        --theme-primary-rgb: var(--bs-primary-rgb, 13, 110, 253);
    }

    /* 动画效果 */
    @keyframes fadeInUp {
        from { opacity: 0; transform: translateY(30px); }
        to { opacity: 1; transform: translateY(0); }
    }

    @keyframes pulse {
        0%, 100% { transform: scale(1); }
        50% { transform: scale(1.05); }
    }

    @keyframes float {
        0%, 100% { transform: translateY(0px); }
        50% { transform: translateY(-10px); }
    }

    /* Hero区域 - 现代化设计 */
    .hero-section {
        min-height: 100vh !important;
        background: var(--hero-gradient) !important;
        position: relative;
        display: flex;
        align-items: center;
        overflow: hidden;
        padding: 2rem 0;
    }

    .hero-section::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background:
            radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
            radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.3) 0%, transparent 50%),
            url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
        opacity: 0.4;
    }

    .hero-section::after {
        content: '';
        position: absolute;
        bottom: 0;
        left: 0;
        right: 0;
        height: 100px;
        background: linear-gradient(to top, rgba(248, 249, 250, 0.1), transparent);
    }

    .hero-content {
        position: relative;
        z-index: 2;
        animation: fadeInUp 1s ease-out;
    }

    .hero-title {
        font-size: clamp(2.5rem, 5vw, 4rem);
        font-weight: 800;
        color: white;
        margin-bottom: 1.5rem;
        text-shadow: 0 4px 8px rgba(0,0,0,0.3);
        line-height: 1.2;
        background: linear-gradient(45deg, #ffffff, #f0f9ff);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
    }

    .hero-subtitle {
        font-size: clamp(1.1rem, 2.5vw, 1.4rem);
        color: rgba(255,255,255,0.95);
        margin-bottom: 2.5rem;
        font-weight: 400;
        line-height: 1.6;
        text-shadow: 0 2px 4px rgba(0,0,0,0.2);
    }

    .hero-features {
        margin: 2rem 0;
    }

    .hero-feature-item {
        display: flex;
        align-items: center;
        margin-bottom: 1rem;
        color: rgba(255,255,255,0.9);
        font-size: 1rem;
    }

    .hero-feature-item i {
        margin-right: 0.75rem;
        color: #fbbf24;
        font-size: 1.2rem;
    }

    /* Hero视觉元素样式 */
    .hero-visual-container {
        position: relative;
        height: 400px;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .hero-main-visual {
        position: relative;
        z-index: 2;
    }

    .hero-laptop {
        font-size: 12rem;
        color: rgba(255,255,255,0.15);
        animation: float 6s ease-in-out infinite;
        filter: drop-shadow(0 10px 20px rgba(0,0,0,0.2));
    }

    .hero-decorations {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        pointer-events: none;
    }

    .decoration-item {
        position: absolute;
        color: rgba(255,255,255,0.3);
        font-size: 2rem;
        animation: float 4s ease-in-out infinite;
    }

    .decoration-1 {
        top: 20%;
        left: 10%;
        animation-delay: -1s;
    }

    .decoration-2 {
        top: 60%;
        right: 15%;
        animation-delay: -2s;
    }

    .decoration-3 {
        bottom: 20%;
        left: 20%;
        animation-delay: -3s;
    }

    /* 响应式优化 - 确保在所有设备上正确显示 */
    @media (max-width: 991.98px) {
        .hero-laptop {
            font-size: 8rem !important;
        }

        .hero-visual-container {
            height: 300px !important;
        }

        .decoration-item {
            font-size: 1.5rem !important;
        }
    }

    @media (max-width: 767.98px) {
        .hero-title {
            font-size: 2.5rem !important;
        }

        .hero-subtitle {
            font-size: 1.1rem !important;
        }

        .btn-hero {
            padding: 0.875rem 2rem !important;
            font-size: 1rem !important;
            margin: 0.25rem !important;
            width: 100% !important;
            justify-content: center !important;
        }

        .hero-buttons {
            flex-direction: column !important;
            align-items: stretch !important;
        }

        .website-showcase {
            margin-top: 2rem !important;
        }

        .hero-section {
            padding: 1rem 0 !important;
        }
    }

    .btn-hero {
        padding: 1rem 2.5rem;
        font-size: 1.1rem;
        font-weight: 600;
        border-radius: 3rem;
        border: none;
        margin: 0.5rem;
        transition: var(--transition-smooth);
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
        position: relative;
        overflow: hidden;
    }

    .btn-hero::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
        transition: left 0.5s;
    }

    .btn-hero:hover::before {
        left: 100%;
    }

    .btn-hero-primary {
        background: linear-gradient(45deg, #ffffff, #f8fafc);
        color: var(--bs-primary);
        box-shadow: 0 10px 30px rgba(255,255,255,0.3);
        border: 2px solid rgba(255,255,255,0.2);
    }

    .btn-hero-primary:hover {
        transform: translateY(-3px) scale(1.02);
        box-shadow: 0 20px 40px rgba(255,255,255,0.4);
        color: var(--bs-primary);
        text-decoration: none;
    }

    .btn-hero-outline {
        background: rgba(255,255,255,0.1);
        color: white;
        border: 2px solid rgba(255,255,255,0.3);
        backdrop-filter: blur(10px);
    }

    .btn-hero-outline:hover {
        background: rgba(255,255,255,0.2);
        color: white;
        transform: translateY(-3px) scale(1.02);
        text-decoration: none;
        border-color: rgba(255,255,255,0.5);
    }

    /* 浮动元素 */
    .floating-elements {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        pointer-events: none;
        z-index: 1;
    }

    .floating-icon {
        position: absolute;
        color: rgba(255,255,255,0.1);
        animation: float 6s ease-in-out infinite;
    }

    .floating-icon:nth-child(1) { top: 20%; left: 10%; animation-delay: 0s; }
    .floating-icon:nth-child(2) { top: 60%; left: 85%; animation-delay: 2s; }
    .floating-icon:nth-child(3) { top: 80%; left: 20%; animation-delay: 4s; }
    .floating-icon:nth-child(4) { top: 30%; left: 80%; animation-delay: 1s; }
    .floating-icon:nth-child(5) { top: 70%; left: 60%; animation-delay: 3s; }

    /* 网址展示区域样式 */
    .website-showcase {
        animation: fadeInUp 1.2s ease-out 0.5s both;
    }

    .website-url-card {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(10px);
        border-radius: 20px;
        padding: 25px 30px;
        display: flex;
        align-items: center;
        box-shadow: 0 15px 40px rgba(0, 0, 0, 0.1);
        border: 2px solid rgba(255, 255, 255, 0.3);
        transition: all 0.3s ease;
        max-width: 600px;
        margin: 0 auto;
    }

    .website-url-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15);
    }

    .url-icon {
        width: 60px;
        height: 60px;
        background: var(--hero-gradient);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 1.8rem;
        margin-right: 20px;
        flex-shrink: 0;
    }

    .url-content {
        flex: 1;
        text-align: left;
    }

    .url-label {
        font-size: 0.9rem;
        color: #666;
        font-weight: 500;
        margin-bottom: 5px;
    }

    .url-address {
        font-size: 1.8rem;
        font-weight: 800;
        color: var(--bs-primary);
        margin-bottom: 5px;
        letter-spacing: 1px;
        text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }

    .url-subtitle {
        font-size: 0.85rem;
        color: #888;
        font-weight: 400;
    }

    .url-copy {
        margin-left: 15px;
    }

    .copy-btn {
        width: 45px;
        height: 45px;
        background: var(--hero-gradient);
        border: none;
        border-radius: 50%;
        color: white;
        font-size: 1.2rem;
        cursor: pointer;
        transition: all 0.3s ease;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .copy-btn:hover {
        transform: scale(1.1);
        box-shadow: 0 8px 20px rgba(13, 110, 253, 0.4);
    }

    .copy-btn:active {
        transform: scale(0.95);
    }

        /* 复制成功提示 */
        .copy-success {
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: rgba(40, 167, 69, 0.95);
            color: white;
            padding: 15px 25px;
            border-radius: 10px;
            font-weight: 600;
            z-index: 9999;
            animation: copySuccessAnimation 2s ease-out forwards;
        }

        @keyframes copySuccessAnimation {
            0% {
                opacity: 0;
                transform: translate(-50%, -50%) scale(0.8);
            }
            20% {
                opacity: 1;
                transform: translate(-50%, -50%) scale(1);
            }
            80% {
                opacity: 1;
                transform: translate(-50%, -50%) scale(1);
            }
            100% {
                opacity: 0;
                transform: translate(-50%, -50%) scale(0.8);
            }
        }

        /* 页脚网址展示样式 */
        .website-url-footer {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 30px;
            border: 2px solid rgba(255, 255, 255, 0.2);
            transition: all 0.3s ease;
        }

        .website-url-footer:hover {
            background: rgba(255, 255, 255, 0.15);
            border-color: rgba(255, 255, 255, 0.3);
            transform: translateY(-3px);
        }

        .url-footer-content {
            display: flex;
            align-items: center;
            justify-content: center;
            text-align: center;
        }

        .url-footer-icon {
            width: 70px;
            height: 70px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 2rem;
            margin-right: 25px;
            flex-shrink: 0;
        }

        .url-footer-text {
            flex: 1;
        }

        .url-footer-address {
            font-size: 2.2rem;
            font-weight: 800;
            color: white;
            margin: 10px 0;
            letter-spacing: 2px;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
            animation: pulse 3s infinite;
        }

        .url-footer-copy {
            margin-left: 25px;
        }

        .copy-btn-footer {
            width: 55px;
            height: 55px;
            background: rgba(255, 255, 255, 0.2);
            border: 2px solid rgba(255, 255, 255, 0.3);
            border-radius: 50%;
            color: white;
            font-size: 1.3rem;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .copy-btn-footer:hover {
            background: rgba(255, 255, 255, 0.3);
            border-color: rgba(255, 255, 255, 0.5);
            transform: scale(1.1);
            box-shadow: 0 8px 20px rgba(255, 255, 255, 0.2);
        }

        .copy-btn-footer:active {
            transform: scale(0.95);
        }

        /* 页脚网址响应式设计 */
        @media (max-width: 768px) {
            .url-footer-content {
                flex-direction: column;
                text-align: center;
            }

            .url-footer-icon {
                margin-right: 0;
                margin-bottom: 20px;
            }

            .url-footer-address {
                font-size: 1.8rem;
            }

            .url-footer-copy {
                margin-left: 0;
                margin-top: 20px;
            }
        }

        /* 响应式优化 */
        @media (max-width: 767.98px) {
            .hero-title {
                font-size: 2.5rem;
            }

            .hero-subtitle {
                font-size: 1.1rem;
            }

            .btn-hero {
                padding: 0.875rem 2rem;
                font-size: 1rem;
                margin: 0.25rem;
                width: 100%;
                justify-content: center;
            }

            .hero-buttons {
                flex-direction: column;
                align-items: stretch;
            }

            .website-showcase {
                margin-top: 2rem;
            }
        }

        /* 特色功能区域 - 现代化设计 */
        .features-section {
            padding: var(--section-padding);
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
            position: relative;
        }

        [data-bs-theme="dark"] .features-section {
            background: linear-gradient(135deg, #1e293b 0%, #0f172a 100%);
        }

        .features-section::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="50" cy="50" r="1" fill="rgba(99,102,241,0.1)"/></svg>') repeat;
            opacity: 0.5;
        }

        .feature-card {
            background: rgba(255,255,255,0.9);
            backdrop-filter: blur(10px);
            border-radius: var(--border-radius-lg);
            padding: 2.5rem 2rem;
            text-align: center;
            box-shadow: var(--card-shadow);
            transition: var(--transition-smooth);
            height: 100%;
            border: 1px solid rgba(255,255,255,0.2);
            position: relative;
            overflow: hidden;
        }

        [data-bs-theme="dark"] .feature-card {
            background: rgba(30, 41, 59, 0.9);
            border-color: rgba(255,255,255,0.1);
        }

        .feature-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: var(--hero-gradient);
            transform: scaleX(0);
            transition: transform 0.3s ease;
        }

        .feature-card:hover {
            transform: translateY(-10px) scale(1.02);
            box-shadow: var(--card-hover-shadow);
        }

        .feature-card:hover::before {
            transform: scaleX(1);
        }

        .feature-icon {
            width: 5rem;
            height: 5rem;
            background: var(--hero-gradient);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 1.5rem;
            color: white;
            font-size: 2rem;
            position: relative;
            box-shadow: 0 8px 25px rgba(99, 102, 241, 0.3);
        }

        .feature-icon::after {
            content: '';
            position: absolute;
            inset: -2px;
            border-radius: 50%;
            background: var(--hero-gradient);
            z-index: -1;
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .feature-card:hover .feature-icon::after {
            opacity: 0.2;
        }

        .feature-title {
            font-size: 1.5rem;
            font-weight: 700;
            color: var(--bs-body-color);
            margin-bottom: 1rem;
        }

        .feature-desc {
            color: var(--bs-secondary-color);
            line-height: 1.6;
            font-size: 1rem;
        }

        /* 视频教学区域 */
        .videos-section {
            padding: 100px 0;
            background: white;
        }

        .section-title {
            text-align: center;
            font-size: 2.5rem;
            font-weight: 800;
            color: #333;
            margin-bottom: 20px;
        }

        .section-subtitle {
            text-align: center;
            font-size: 1.2rem;
            color: #666;
            margin-bottom: 60px;
        }

        .video-card {
            background: white;
            border-radius: 15px;
            overflow: hidden;
            box-shadow: var(--card-shadow);
            transition: all 0.3s ease;
            margin-bottom: 30px;
            cursor: pointer;
        }

        .video-card:hover {
            transform: translateY(-5px);
            box-shadow: var(--card-hover-shadow);
        }

        .video-thumbnail {
            position: relative;
            height: 200px;
            background: var(--hero-gradient);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 3rem;
            overflow: hidden;
            border-radius: 15px 15px 0 0;
        }

        .video-info {
            padding: 20px;
        }

        .video-title {
            font-size: 1.2rem;
            font-weight: 600;
            color: #333;
            margin-bottom: 10px;
        }

        .video-desc {
            color: #666;
            font-size: 0.9rem;
        }

        /* 分页控制器样式 */
        .video-pagination {
            display: flex;
            justify-content: center;
            align-items: center;
            margin-top: 40px;
            gap: 15px;
        }

        .pagination-btn {
            background: var(--hero-gradient);
            color: white;
            border: none;
            border-radius: 50%;
            width: 50px;
            height: 50px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.2rem;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(var(--theme-primary-rgb), 0.3);
        }

        .pagination-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(var(--theme-primary-rgb), 0.4);
        }

        .pagination-btn:disabled {
            background: #ccc;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }

        .pagination-info {
            background: white;
            padding: 10px 20px;
            border-radius: 25px;
            box-shadow: var(--card-shadow);
            font-weight: 600;
            color: #333;
        }

        /* 视频卡片动画 */
        .video-card {
            opacity: 0;
            transform: translateY(20px);
            transition: all 0.5s ease;
        }

        .video-card.show {
            opacity: 1;
            transform: translateY(0);
        }

        .video-card.hide {
            opacity: 0;
            transform: translateY(-20px);
        }

        /* 加载动画 */
        .videos-loading {
            text-align: center;
            padding: 60px 0;
        }

        .loading-spinner {
            width: 50px;
            height: 50px;
            border: 4px solid rgba(var(--theme-primary-rgb), 0.1);
            border-left: 4px solid var(--theme-primary);
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 0 auto 20px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* 新用户入门指南样式 */
        .guide-steps .col-lg {
            flex: 1;
            min-width: 0;
        }

        .guide-steps .feature-card {
            height: 100%;
            min-height: 200px;
        }

        /* 工作流程样式 */
        .workflow-steps .col-lg {
            flex: 1;
            min-width: 0;
        }

        .workflow-steps .feature-card {
            height: 100%;
        }

        /* 财务管理模块样式 */
        .financial-modules .col-lg {
            flex: 1;
            min-width: 0;
        }

        .financial-modules .feature-card {
            height: 100%;
            transition: all 0.3s ease;
        }

        .financial-modules .feature-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 35px rgba(0,0,0,0.1);
        }

        /* 响应式设计 */
        @media (max-width: 1200px) {
            .guide-steps .col-lg {
                flex: 0 0 calc(25% - 15px);
                max-width: calc(25% - 15px);
            }

            .workflow-steps .col-lg {
                flex: 0 0 calc(25% - 15px);
                max-width: calc(25% - 15px);
            }

            .financial-modules .col-lg {
                flex: 0 0 calc(25% - 15px);
                max-width: calc(25% - 15px);
            }
        }

        @media (max-width: 992px) {
            .guide-steps .col-lg {
                flex: 0 0 calc(33.333% - 15px);
                max-width: calc(33.333% - 15px);
            }

            .workflow-steps .col-lg {
                flex: 0 0 calc(50% - 15px);
                max-width: calc(50% - 15px);
            }

            .financial-modules .col-lg {
                flex: 0 0 calc(50% - 15px);
                max-width: calc(50% - 15px);
            }
        }

        @media (max-width: 768px) {
            .hero-title {
                font-size: 2.5rem;
            }

            .hero-subtitle {
                font-size: 1.1rem;
            }

            .btn-hero {
                display: block;
                margin: 10px 0;
                width: 100%;
            }

            /* 移动端网址展示优化 */
            .website-url-card {
                flex-direction: column;
                text-align: center;
                padding: 20px;
            }

            .url-icon {
                margin-right: 0;
                margin-bottom: 15px;
            }

            .url-content {
                text-align: center;
                margin-bottom: 15px;
            }

            .url-address {
                font-size: 1.5rem;
            }

            .url-copy {
                margin-left: 0;
            }

            .guide-steps .col-lg {
                flex: 0 0 calc(50% - 15px);
                max-width: calc(50% - 15px);
            }

            .workflow-steps .col-lg {
                flex: 0 0 calc(50% - 15px);
                max-width: calc(50% - 15px);
            }

            .financial-modules .col-lg {
                flex: 0 0 calc(50% - 15px);
                max-width: calc(50% - 15px);
            }
        }

        @media (max-width: 576px) {
            .guide-steps .col-lg,
            .workflow-steps .col-lg,
            .financial-modules .col-lg {
                flex: 0 0 100%;
                max-width: 100%;
            }
        }

        /* 快速导航和快速链接优化样式 */
        .quick-nav-btn {
            transition: all 0.3s ease !important;
            position: relative;
            overflow: hidden;
        }

        .quick-nav-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(255,255,255,0.3) !important;
            background: rgba(255,255,255,0.2) !important;
            border-color: rgba(255,255,255,0.8) !important;
        }

        .quick-link-btn {
            transition: all 0.3s ease !important;
            position: relative;
            overflow: hidden;
        }

        .quick-link-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 30px rgba(0,0,0,0.2) !important;
        }

        .quick-link-btn.btn-primary:hover {
            background: linear-gradient(135deg, #007bff 0%, #0056b3 100%) !important;
            box-shadow: 0 8px 30px rgba(0,123,255,0.4) !important;
        }

        .quick-link-btn.btn-success:hover {
            background: linear-gradient(135deg, #28a745 0%, #1e7e34 100%) !important;
            box-shadow: 0 8px 30px rgba(40,167,69,0.4) !important;
        }

        .quick-link-btn.btn-info:hover {
            background: linear-gradient(135deg, #17a2b8 0%, #117a8b 100%) !important;
            box-shadow: 0 8px 30px rgba(23,162,184,0.4) !important;
        }

        .quick-link-btn.btn-danger:hover {
            background: linear-gradient(135deg, #dc3545 0%, #bd2130 100%) !important;
            box-shadow: 0 8px 30px rgba(220,53,69,0.4) !important;
        }

        /* 按钮内图标动画 */
        .quick-link-btn i {
            transition: transform 0.3s ease;
        }

        .quick-link-btn:hover i {
            transform: scale(1.1);
        }

        /* 响应式优化 */
        @media (max-width: 768px) {
            .quick-nav-btn, .quick-link-btn {
                margin-bottom: 15px !important;
                padding: 15px 15px !important;
            }

            .quick-link-btn i {
                font-size: 1.3rem !important;
            }
        }
    </style>
{% endblock %}

{% block content %}

    <!-- Hero区域 -->
    <section class="hero-section" id="home">
        <div class="floating-elements">
            <i class="fas fa-utensils floating-icon" style="font-size: 3rem;"></i>
            <i class="fas fa-leaf floating-icon" style="font-size: 2.5rem;"></i>
            <i class="fas fa-chart-line floating-icon" style="font-size: 2rem;"></i>
            <i class="fas fa-shield-alt floating-icon" style="font-size: 2.8rem;"></i>
            <i class="fas fa-users floating-icon" style="font-size: 2.3rem;"></i>
        </div>
        <div class="container">
            <div class="row align-items-center min-vh-100 py-5">
                <div class="col-lg-6 order-2 order-lg-1">
                    <div class="hero-content">
                        <div class="mb-4">
                            <span class="badge bg-light text-primary px-3 py-2 rounded-pill mb-3">
                                <i class="fas fa-star me-2"></i>专业校园餐饮管理
                            </span>
                        </div>
                        <h1 class="hero-title">智慧食堂平台</h1>
                        <p class="hero-subtitle">
                            专业的校园餐饮管理解决方案<br>
                            从供应商到餐桌的全链路追踪管理<br>
                            实现食品安全可视化、可管控、可追溯
                        </p>
                        <div class="hero-features">
                            <div class="row g-3">
                                <div class="col-md-6">
                                    <div class="hero-feature-item">
                                        <i class="fas fa-shield-check"></i>
                                        <span>食品安全追溯</span>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="hero-feature-item">
                                        <i class="fas fa-boxes"></i>
                                        <span>智能库存管理</span>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="hero-feature-item">
                                        <i class="fas fa-chart-pie"></i>
                                        <span>财务成本控制</span>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="hero-feature-item">
                                        <i class="fas fa-heartbeat"></i>
                                        <span>营养健康管理</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <!-- 网址展示区域 -->
                        <div class="website-showcase mt-4 mb-4">
                            <div class="website-url-card">
                                <div class="url-icon">
                                    <i class="fas fa-globe"></i>
                                </div>
                                <div class="url-content">
                                    <div class="url-label">官方网址</div>
                                    <div class="url-address">xiaoyuanst.com</div>
                                    <div class="url-subtitle">记住我们的网址，随时访问智慧食堂平台</div>
                                </div>
                                <div class="url-copy">
                                    <button class="copy-btn" onclick="copyWebsiteUrl()">
                                        <i class="fas fa-copy"></i>
                                    </button>
                                </div>
                            </div>
                        </div>

                        <div class="hero-buttons mt-4 d-flex flex-wrap justify-content-start">
                            <a href="{{ url_for('auth.guest_login') }}" class="btn-hero btn-hero-primary">
                                <i class="fas fa-rocket"></i>
                                <span>立即体验</span>
                            </a>
                            <a href="#features" class="btn-hero btn-hero-outline">
                                <i class="fas fa-info-circle"></i>
                                <span>了解更多</span>
                            </a>
                        </div>
                    </div>
                </div>
                <div class="col-lg-6 order-1 order-lg-2 mb-5 mb-lg-0">
                    <div class="hero-image text-center position-relative">
                        <div class="hero-visual-container">
                            <!-- 主要视觉元素 -->
                            <div class="hero-main-visual">
                                <i class="fas fa-laptop-code hero-laptop"></i>
                                <!-- 浮动装饰元素 -->
                                <div class="hero-decorations">
                                    <div class="decoration-item decoration-1">
                                        <i class="fas fa-utensils"></i>
                                    </div>
                                    <div class="decoration-item decoration-2">
                                        <i class="fas fa-chart-bar"></i>
                                    </div>
                                    <div class="decoration-item decoration-3">
                                        <i class="fas fa-shield-alt"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- 特色功能区域 -->
    <section class="features-section" id="features">
        <div class="container">
            <div class="row">
                <div class="col-12 text-center mb-5">
                    <h2 class="section-title">核心功能</h2>
                    <p class="section-subtitle">全方位的食堂管理解决方案</p>
                </div>
            </div>
            <div class="row">
                <div class="col-lg-4 col-md-6 mb-4">
                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="fas fa-calculator"></i>
                        </div>
                        <h3 class="feature-title">财务管理</h3>
                        <p class="feature-desc">专业的财务管理系统，包含会计科目管理、财务凭证创建、资产负债表和利润表等财务报表，为学校食堂提供完整的财务解决方案。</p>
                    </div>
                </div>
                <div class="col-lg-4 col-md-6 mb-4">
                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="fas fa-calendar-day"></i>
                        </div>
                        <h3 class="feature-title">日常管理</h3>
                        <p class="feature-desc">全面的日常运营管理，包括每日菜单规划与营养搭配、食品安全检查与质量监控、食品留样记录管理，确保食堂日常运营规范有序。</p>
                    </div>
                </div>
                <div class="col-lg-4 col-md-6 mb-4">
                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="fas fa-truck"></i>
                        </div>
                        <h3 class="feature-title">供应链管理</h3>
                        <p class="feature-desc">完整的供应链解决方案，涵盖采购订单创建与管理、供应商管理、库存实时监控、入库出库管理，实现供应链全流程数字化。</p>
                    </div>
                </div>
                <div class="col-lg-4 col-md-6 mb-4">
                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="fas fa-shield-alt"></i>
                        </div>
                        <h3 class="feature-title">食品安全追溯</h3>
                        <p class="feature-desc">建立完整的食品安全追溯体系，从原料采购到餐桌的全程记录，确保食品安全可视化、可管控、可追溯，保障师生用餐安全。</p>
                    </div>
                </div>
                <div class="col-lg-4 col-md-6 mb-4">
                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="fas fa-cogs"></i>
                        </div>
                        <h3 class="feature-title">系统设置</h3>
                        <p class="feature-desc">灵活的系统配置管理，支持多角色权限设置、用户管理、系统参数配置，满足不同学校的个性化管理需求。</p>
                    </div>
                </div>
                <div class="col-lg-4 col-md-6 mb-4">
                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="fas fa-headset"></i>
                        </div>
                        <h3 class="feature-title">技术支持</h3>
                        <p class="feature-desc">提供完善的技术支持服务，包括在线帮助文档、故障排除指南、技术支持热线(18373062333)和邮件支持，确保系统稳定运行。</p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- 新用户入门指南 -->
    <section class="features-section" id="guide" style="background: white;">
        <div class="container">
            <div class="row">
                <div class="col-12 text-center mb-5">
                    <h2 class="section-title">新用户入门指南</h2>
                    <p class="section-subtitle">7个简单步骤，快速上手智慧食堂管理</p>
                </div>
            </div>
            <div class="row guide-steps">
                <div class="col-lg col-md-6 mb-4">
                    <div class="feature-card" style="border-left: 4px solid #ff6b35; padding: 20px 15px;">
                        <div class="feature-icon" style="background: #ff6b35; width: 60px; height: 60px; font-size: 1.5rem; margin: 0 auto 15px;">
                            <i class="fas fa-user-plus"></i>
                        </div>
                        <h4 class="feature-title" style="font-size: 1rem; margin-bottom: 10px;">1. 注册登录</h4>
                        <p class="feature-desc" style="font-size: 0.85rem; line-height: 1.4;">使用学校信息注册账号，开启智慧食堂管理之旅</p>
                    </div>
                </div>
                <div class="col-lg col-md-6 mb-4">
                    <div class="feature-card" style="border-left: 4px solid #28a745; padding: 20px 15px;">
                        <div class="feature-icon" style="background: #28a745; width: 60px; height: 60px; font-size: 1.5rem; margin: 0 auto 15px;">
                            <i class="fas fa-edit"></i>
                        </div>
                        <h4 class="feature-title" style="font-size: 1rem; margin-bottom: 10px;">2. 完善信息</h4>
                        <p class="feature-desc" style="font-size: 0.85rem; line-height: 1.4;">补充学校基础信息，建立完整的管理档案</p>
                    </div>
                </div>
                <div class="col-lg col-md-6 mb-4">
                    <div class="feature-card" style="border-left: 4px solid #17a2b8; padding: 20px 15px;">
                        <div class="feature-icon" style="background: #17a2b8; width: 60px; height: 60px; font-size: 1.5rem; margin: 0 auto 15px;">
                            <i class="fas fa-truck"></i>
                        </div>
                        <h4 class="feature-title" style="font-size: 1rem; margin-bottom: 10px;">3. 添加供应商</h4>
                        <p class="feature-desc" style="font-size: 0.85rem; line-height: 1.4;">建立供应商档案，构建稳定的供应链体系</p>
                    </div>
                </div>
                <div class="col-lg col-md-6 mb-4">
                    <div class="feature-card" style="border-left: 4px solid #e67e22; padding: 20px 15px;">
                        <div class="feature-icon" style="background: #e67e22; width: 60px; height: 60px; font-size: 1.5rem; margin: 0 auto 15px;">
                            <i class="fas fa-apple-alt"></i>
                        </div>
                        <h4 class="feature-title" style="font-size: 1rem; margin-bottom: 10px;">4. 录入食材</h4>
                        <p class="feature-desc" style="font-size: 0.85rem; line-height: 1.4;">建立食材基础数据库，为菜单制定做准备</p>
                    </div>
                </div>
                <div class="col-lg col-md-6 mb-4">
                    <div class="feature-card" style="border-left: 4px solid #e74c3c; padding: 20px 15px;">
                        <div class="feature-icon" style="background: #e74c3c; width: 60px; height: 60px; font-size: 1.5rem; margin: 0 auto 15px;">
                            <i class="fas fa-clipboard-list"></i>
                        </div>
                        <h4 class="feature-title" style="font-size: 1rem; margin-bottom: 10px;">5. 制定食谱</h4>
                        <p class="feature-desc" style="font-size: 0.85rem; line-height: 1.4;">创建营养均衡的食谱，确保膳食营养搭配合理</p>
                    </div>
                </div>
                <div class="col-lg col-md-6 mb-4">
                    <div class="feature-card" style="border-left: 4px solid #9b59b6; padding: 20px 15px;">
                        <div class="feature-icon" style="background: #9b59b6; width: 60px; height: 60px; font-size: 1.5rem; margin: 0 auto 15px;">
                            <i class="fas fa-calendar-week"></i>
                        </div>
                        <h4 class="feature-title" style="font-size: 1rem; margin-bottom: 10px;">6. 安排菜单</h4>
                        <p class="feature-desc" style="font-size: 0.85rem; line-height: 1.4;">制定周菜单计划，科学安排每日膳食</p>
                    </div>
                </div>
                <div class="col-lg col-md-6 mb-4">
                    <div class="feature-card" style="border-left: 4px solid #27ae60; padding: 20px 15px;">
                        <div class="feature-icon" style="background: #27ae60; width: 60px; height: 60px; font-size: 1.5rem; margin: 0 auto 15px;">
                            <i class="fas fa-play-circle"></i>
                        </div>
                        <h4 class="feature-title" style="font-size: 1rem; margin-bottom: 10px;">7. 开始使用</h4>
                        <p class="feature-desc" style="font-size: 0.85rem; line-height: 1.4;">进入日常管理流程，享受智慧食堂带来的便利</p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- 日常操作流程 -->
    <section class="features-section" id="workflow" style="background: #f8f9fa;">
        <div class="container">
            <div class="row">
                <div class="col-12 text-center mb-5">
                    <h2 class="section-title">日常操作流程</h2>
                    <p class="section-subtitle">标准化的日常管理流程，让食堂运营更加规范高效</p>
                </div>
            </div>
            <div class="row workflow-steps">
                <div class="col-lg col-md-6 mb-4">
                    <div class="feature-card" style="padding: 20px 15px; min-height: 280px;">
                        <div class="feature-icon" style="background: #ff6b35; width: 60px; height: 60px; font-size: 1.5rem; margin: 0 auto 15px;">
                            <i class="fas fa-calendar-alt"></i>
                        </div>
                        <h3 class="feature-title" style="font-size: 1.1rem; margin-bottom: 10px;">制定周菜单</h3>
                        <p class="feature-desc" style="font-size: 0.85rem; margin-bottom: 15px;">每周制定下周菜单，科学营养搭配</p>
                        <div style="text-align: left;">
                            <small class="text-muted" style="font-size: 0.75rem;">
                                <i class="fas fa-arrow-right me-1"></i>拖拽式菜品安排<br>
                                <i class="fas fa-arrow-right me-1"></i>营养分析报告<br>
                                <i class="fas fa-arrow-right me-1"></i>成本控制管理
                            </small>
                        </div>
                    </div>
                </div>
                <div class="col-lg col-md-6 mb-4">
                    <div class="feature-card" style="padding: 20px 15px; min-height: 280px;">
                        <div class="feature-icon" style="background: #28a745; width: 60px; height: 60px; font-size: 1.5rem; margin: 0 auto 15px;">
                            <i class="fas fa-shopping-cart"></i>
                        </div>
                        <h3 class="feature-title" style="font-size: 1.1rem; margin-bottom: 10px;">生成采购单</h3>
                        <p class="feature-desc" style="font-size: 0.85rem; margin-bottom: 15px;">根据菜单自动生成采购单，提高效率</p>
                        <div style="text-align: left;">
                            <small class="text-muted" style="font-size: 0.75rem;">
                                <i class="fas fa-arrow-right me-1"></i>采购订单管理<br>
                                <i class="fas fa-arrow-right me-1"></i>供应商选择<br>
                                <i class="fas fa-arrow-right me-1"></i>价格比较分析
                            </small>
                        </div>
                    </div>
                </div>
                <div class="col-lg col-md-6 mb-4">
                    <div class="feature-card" style="padding: 20px 15px; min-height: 280px;">
                        <div class="feature-icon" style="background: #17a2b8; width: 60px; height: 60px; font-size: 1.5rem; margin: 0 auto 15px;">
                            <i class="fas fa-warehouse"></i>
                        </div>
                        <h3 class="feature-title" style="font-size: 1.1rem; margin-bottom: 10px;">库存管理</h3>
                        <p class="feature-desc" style="font-size: 0.85rem; margin-bottom: 15px;">精确管理食材库存，避免浪费</p>
                        <div style="text-align: left;">
                            <small class="text-muted" style="font-size: 0.75rem;">
                                <i class="fas fa-arrow-right me-1"></i>入库出库管理<br>
                                <i class="fas fa-arrow-right me-1"></i>库存预警提醒<br>
                                <i class="fas fa-arrow-right me-1"></i>先进先出原则
                            </small>
                        </div>
                    </div>
                </div>
                <div class="col-lg col-md-6 mb-4">
                    <div class="feature-card" style="padding: 20px 15px; min-height: 280px;">
                        <div class="feature-icon" style="background: #e67e22; width: 60px; height: 60px; font-size: 1.5rem; margin: 0 auto 15px;">
                            <i class="fas fa-search"></i>
                        </div>
                        <h3 class="feature-title" style="font-size: 1.1rem; margin-bottom: 10px;">日常检查</h3>
                        <p class="feature-desc" style="font-size: 0.85rem; margin-bottom: 15px;">记录各项检查结果，确保食品安全</p>
                        <div style="text-align: left;">
                            <small class="text-muted" style="font-size: 0.75rem;">
                                <i class="fas fa-arrow-right me-1"></i>食品安全检查<br>
                                <i class="fas fa-arrow-right me-1"></i>留样记录管理<br>
                                <i class="fas fa-arrow-right me-1"></i>问题快速定位
                            </small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- 财务管理功能指南 -->
    <section class="features-section" id="financial-guide" style="background: white;">
        <div class="container">
            <div class="row">
                <div class="col-12 text-center mb-5">
                    <h2 class="section-title">完整的财务管理功能指南</h2>
                    <p class="section-subtitle">专业的财务管理系统，满足学校食堂的所有财务需求</p>
                </div>
            </div>

            <!-- 快速导航 -->
            <div class="row mb-5">
                <div class="col-12">
                    <div class="feature-card" style="background: var(--hero-gradient); color: white; text-align: center; padding: 40px 30px;">
                        <h4 style="color: white; margin-bottom: 30px; font-weight: 600;">
                            <i class="fas fa-rocket mr-3" style="font-size: 1.3rem;"></i>快速导航
                        </h4>
                        <div class="row">
                            <div class="col-lg-3 col-md-6 mb-3">
                                <a href="#accounting-subjects" class="btn btn-outline-light btn-lg w-100 quick-nav-btn" style="padding: 15px 20px; font-size: 1rem; border-width: 2px;">
                                    <i class="fas fa-chart-line me-2" style="font-size: 1.1rem;"></i>
                                    <div style="font-weight: 500;">会计科目管理</div>
                                </a>
                            </div>
                            <div class="col-lg-3 col-md-6 mb-3">
                                <a href="#vouchers" class="btn btn-outline-light btn-lg w-100 quick-nav-btn" style="padding: 15px 20px; font-size: 1rem; border-width: 2px;">
                                    <i class="fas fa-file-invoice me-2" style="font-size: 1.1rem;"></i>
                                    <div style="font-weight: 500;">财务凭证</div>
                                </a>
                            </div>
                            <div class="col-lg-3 col-md-6 mb-3">
                                <a href="#reports" class="btn btn-outline-light btn-lg w-100 quick-nav-btn" style="padding: 15px 20px; font-size: 1rem; border-width: 2px;">
                                    <i class="fas fa-chart-bar me-2" style="font-size: 1.1rem;"></i>
                                    <div style="font-weight: 500;">财务报表</div>
                                </a>
                            </div>
                            <div class="col-lg-3 col-md-6 mb-3">
                                <a href="#payables" class="btn btn-outline-light btn-lg w-100 quick-nav-btn" style="padding: 15px 20px; font-size: 1rem; border-width: 2px;">
                                    <i class="fas fa-credit-card me-2" style="font-size: 1.1rem;"></i>
                                    <div style="font-weight: 500;">应付账款</div>
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 财务功能详细介绍 -->
            <div class="row financial-modules">
                <!-- 会计科目管理 -->
                <div class="col-lg col-md-6 mb-4" id="accounting-subjects">
                    <div class="feature-card" style="padding: 25px 20px; min-height: 400px; border-left: 4px solid #007bff;">
                        <div class="feature-icon" style="background: #007bff; width: 70px; height: 70px; font-size: 1.8rem; margin: 0 auto 20px;">
                            <i class="fas fa-chart-line"></i>
                        </div>
                        <h3 class="feature-title" style="font-size: 1.2rem; margin-bottom: 15px; color: #007bff;">会计科目管理</h3>
                        <p class="feature-desc" style="font-size: 0.9rem; margin-bottom: 20px;">会计科目是财务管理的基础，用于分类记录各种经济业务。</p>

                        <div style="text-align: left; margin-bottom: 20px;">
                            <h6 style="color: #333; font-size: 0.9rem; margin-bottom: 10px;">系统科目 vs 学校科目</h6>
                            <div style="background: #f8f9fa; padding: 15px; border-radius: 8px; margin-bottom: 15px;">
                                <div style="display: flex; justify-content: space-between;">
                                    <div style="flex: 1; margin-right: 10px;">
                                        <strong style="color: #28a745; font-size: 0.85rem;">系统科目</strong>
                                        <ul style="font-size: 0.75rem; margin: 5px 0; padding-left: 15px;">
                                            <li>标准化会计科目</li>
                                            <li>全局共享</li>
                                            <li>符合会计准则</li>
                                            <li>不可修改</li>
                                        </ul>
                                    </div>
                                    <div style="flex: 1; margin-left: 10px;">
                                        <strong style="color: #17a2b8; font-size: 0.85rem;">学校科目</strong>
                                        <ul style="font-size: 0.75rem; margin: 5px 0; padding-left: 15px;">
                                            <li>学校专属科目</li>
                                            <li>可自定义修改</li>
                                            <li>基于系统科目</li>
                                            <li>灵活适应需求</li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div style="text-align: center;">
                            <a href="{{ url_for('help.accounting_subjects_help') }}" class="btn btn-primary btn-sm">
                                <i class="fas fa-book me-1"></i>详细指南
                            </a>
                            <a href="{{ url_for('financial.accounting_subjects_index') }}" class="btn btn-success btn-sm ms-2">
                                <i class="fas fa-cogs me-1"></i>管理科目
                            </a>
                        </div>
                    </div>
                </div>

                <!-- 财务凭证 -->
                <div class="col-lg col-md-6 mb-4" id="vouchers">
                    <div class="feature-card" style="padding: 25px 20px; min-height: 400px; border-left: 4px solid #28a745;">
                        <div class="feature-icon" style="background: #28a745; width: 70px; height: 70px; font-size: 1.8rem; margin: 0 auto 20px;">
                            <i class="fas fa-file-invoice"></i>
                        </div>
                        <h3 class="feature-title" style="font-size: 1.2rem; margin-bottom: 15px; color: #28a745;">财务凭证</h3>
                        <p class="feature-desc" style="font-size: 0.9rem; margin-bottom: 20px;">财务凭证是记录经济业务的重要单据，是财务核算的基础。</p>

                        <div style="text-align: left; margin-bottom: 20px;">
                            <h6 style="color: #333; font-size: 0.9rem; margin-bottom: 10px;">主要功能</h6>
                            <ul style="font-size: 0.8rem; margin-bottom: 15px; padding-left: 20px;">
                                <li><strong>创建凭证</strong> - 录入各种经济业务</li>
                                <li><strong>编辑凭证</strong> - 修改凭证信息和明细</li>
                                <li><strong>审核凭证</strong> - 凭证审核流程</li>
                                <li><strong>查询凭证</strong> - 按条件查询凭证</li>
                            </ul>

                            <div style="background: #fff3cd; padding: 12px; border-radius: 6px; border-left: 3px solid #ffc107;">
                                <h6 style="color: #856404; font-size: 0.85rem; margin-bottom: 8px;">常见问题</h6>
                                <p style="font-size: 0.75rem; color: #856404; margin: 0;">
                                    <strong>问题：</strong>添加明细时会计科目下拉框为空<br>
                                    <strong>解决：</strong>需要先初始化会计科目，参考会计科目管理帮助
                                </p>
                            </div>
                        </div>

                        <div style="text-align: center;">
                            <a href="{{ url_for('financial.vouchers_index') }}" class="btn btn-success btn-sm">
                                <i class="fas fa-file-invoice mr-1"></i>财务凭证管理
                            </a>
                        </div>
                    </div>
                </div>

                <!-- 财务报表 -->
                <div class="col-lg col-md-6 mb-4" id="reports">
                    <div class="feature-card" style="padding: 25px 20px; min-height: 400px; border-left: 4px solid #17a2b8;">
                        <div class="feature-icon" style="background: #17a2b8; width: 70px; height: 70px; font-size: 1.8rem; margin: 0 auto 20px;">
                            <i class="fas fa-chart-bar"></i>
                        </div>
                        <h3 class="feature-title" style="font-size: 1.2rem; margin-bottom: 15px; color: #17a2b8;">财务报表</h3>
                        <p class="feature-desc" style="font-size: 0.9rem; margin-bottom: 20px;">财务报表提供学校财务状况的全面分析。</p>

                        <div style="text-align: left; margin-bottom: 20px;">
                            <h6 style="color: #333; font-size: 0.9rem; margin-bottom: 10px;">报表类型</h6>
                            <div style="background: #f8f9fa; padding: 15px; border-radius: 8px;">
                                <ul style="font-size: 0.8rem; margin: 0; padding-left: 20px;">
                                    <li><strong style="color: #007bff;">资产负债表</strong> - 财务状况报表</li>
                                    <li><strong style="color: #28a745;">利润表</strong> - 经营成果报表</li>
                                    <li><strong style="color: #17a2b8;">现金流量表</strong> - 现金流动情况</li>
                                    <li><strong style="color: #ffc107;">科目余额表</strong> - 各科目余额</li>
                                </ul>
                            </div>
                        </div>

                        <div style="text-align: center;">
                            <a href="{{ url_for('financial.reports_index') }}" class="btn btn-info btn-sm">
                                <i class="fas fa-chart-bar mr-1"></i>查看财务报表
                            </a>
                        </div>
                    </div>
                </div>

                <!-- 应付账款 -->
                <div class="col-lg col-md-6 mb-4" id="payables">
                    <div class="feature-card" style="padding: 25px 20px; min-height: 400px; border-left: 4px solid #dc3545;">
                        <div class="feature-icon" style="background: #dc3545; width: 70px; height: 70px; font-size: 1.8rem; margin: 0 auto 20px;">
                            <i class="fas fa-credit-card"></i>
                        </div>
                        <h3 class="feature-title" style="font-size: 1.2rem; margin-bottom: 15px; color: #dc3545;">应付账款</h3>
                        <p class="feature-desc" style="font-size: 0.9rem; margin-bottom: 20px;">管理学校对供应商的应付款项。</p>

                        <div style="text-align: left; margin-bottom: 20px;">
                            <h6 style="color: #333; font-size: 0.9rem; margin-bottom: 10px;">主要功能</h6>
                            <ul style="font-size: 0.8rem; margin-bottom: 15px; padding-left: 20px;">
                                <li><strong>应付登记</strong> - 记录应付款项</li>
                                <li><strong>付款管理</strong> - 处理付款业务</li>
                                <li><strong>账龄分析</strong> - 分析应付账款账龄</li>
                                <li><strong>供应商对账</strong> - 与供应商核对账目</li>
                            </ul>
                        </div>

                        <div style="text-align: center;">
                            <a href="{{ url_for('financial.payables_index') }}" class="btn btn-danger btn-sm">
                                <i class="fas fa-credit-card mr-1"></i>应付账款管理
                            </a>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 快速链接 -->
            <div class="row mt-5">
                <div class="col-12">
                    <div style="background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%); padding: 40px 30px; border-radius: 20px; text-align: center; box-shadow: 0 8px 25px rgba(0,0,0,0.1);">
                        <h3 style="color: #333; margin-bottom: 30px; font-weight: 600;">
                            <i class="fas fa-link mr-3" style="color: #007bff; font-size: 1.3rem;"></i>快速链接
                        </h3>
                        <p style="color: #666; margin-bottom: 30px; font-size: 1rem;">
                            直接访问财务管理各个模块，快速开始您的工作
                        </p>
                        <div class="row">
                            <div class="col-lg-3 col-md-6 mb-3">
                                <a href="{{ url_for('financial.accounting_subjects_index') }}" class="btn btn-primary btn-lg w-100 quick-link-btn" style="padding: 18px 20px; font-size: 1rem; border-radius: 12px; box-shadow: 0 4px 15px rgba(0,123,255,0.3); border: none;">
                                    <i class="fas fa-chart-line mb-2" style="font-size: 1.5rem; display: block;"></i>
                                    <div style="font-weight: 600;">会计科目</div>
                                    <small style="opacity: 0.8;">科目管理</small>
                                </a>
                            </div>
                            <div class="col-lg-3 col-md-6 mb-3">
                                <a href="{{ url_for('financial.vouchers_index') }}" class="btn btn-success btn-lg w-100 quick-link-btn" style="padding: 18px 20px; font-size: 1rem; border-radius: 12px; box-shadow: 0 4px 15px rgba(40,167,69,0.3); border: none;">
                                    <i class="fas fa-file-invoice mb-2" style="font-size: 1.5rem; display: block;"></i>
                                    <div style="font-weight: 600;">财务凭证</div>
                                    <small style="opacity: 0.8;">凭证管理</small>
                                </a>
                            </div>
                            <div class="col-lg-3 col-md-6 mb-3">
                                <a href="{{ url_for('financial.reports_index') }}" class="btn btn-info btn-lg w-100 quick-link-btn" style="padding: 18px 20px; font-size: 1rem; border-radius: 12px; box-shadow: 0 4px 15px rgba(23,162,184,0.3); border: none;">
                                    <i class="fas fa-chart-bar mb-2" style="font-size: 1.5rem; display: block;"></i>
                                    <div style="font-weight: 600;">财务报表</div>
                                    <small style="opacity: 0.8;">报表查看</small>
                                </a>
                            </div>
                            <div class="col-lg-3 col-md-6 mb-3">
                                <a href="{{ url_for('financial.payables_index') }}" class="btn btn-danger btn-lg w-100 quick-link-btn" style="padding: 18px 20px; font-size: 1rem; border-radius: 12px; box-shadow: 0 4px 15px rgba(220,53,69,0.3); border: none;">
                                    <i class="fas fa-credit-card mb-2" style="font-size: 1.5rem; display: block;"></i>
                                    <div style="font-weight: 600;">应付账款</div>
                                    <small style="opacity: 0.8;">账款管理</small>
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- 视频教学区域 -->
    <section class="videos-section" id="videos">
        <div class="container">
            <div class="row">
                <div class="col-12 text-center mb-5">
                    <h2 class="section-title">视频教学</h2>
                    <p class="section-subtitle">快速上手，轻松掌握系统操作</p>
                </div>
            </div>

            <!-- 加载状态 -->
            <div class="videos-loading" id="videosLoading">
                <div class="loading-spinner"></div>
                <p>正在加载视频教学内容...</p>
            </div>

            <!-- 视频内容容器 -->
            <div class="row" id="videosContainer" style="display: none;">
                <!-- 视频内容将通过JavaScript动态加载 -->
            </div>

            <!-- 分页控制器 -->
            <div class="video-pagination" id="videoPagination" style="display: none;">
                <button class="pagination-btn" id="prevBtn" onclick="changePage(-1)">
                    <i class="fas fa-chevron-left"></i>
                </button>
                <div class="pagination-info" id="paginationInfo">
                    第 1 页 / 共 1 页
                </div>
                <button class="pagination-btn" id="nextBtn" onclick="changePage(1)">
                    <i class="fas fa-chevron-right"></i>
                </button>
            </div>
        </div>
    </section>

    <!-- 联系我们区域 -->
    <section class="features-section" style="background: var(--hero-gradient); color: white;">
        <div class="container">
            <div class="row">
                <div class="col-12 text-center mb-5">
                    <h2 class="section-title" style="color: white;">联系我们</h2>
                    <p class="section-subtitle" style="color: rgba(255,255,255,0.9);">专业的技术支持团队，为您提供全方位服务</p>
                </div>
            </div>
            <div class="row">
                <div class="col-lg-4 col-md-6 mb-4">
                    <div class="feature-card" style="background: rgba(255,255,255,0.1); border: 1px solid rgba(255,255,255,0.2);">
                        <div class="feature-icon" style="background: rgba(255,255,255,0.2);">
                            <i class="fas fa-phone"></i>
                        </div>
                        <h3 class="feature-title" style="color: white;">技术支持热线</h3>
                        <p class="feature-desc" style="color: rgba(255,255,255,0.9);">
                            <strong style="font-size: 1.2rem;">18373062333</strong><br>
                            工作日 9:00-18:00<br>
                            专业技术团队为您解答
                        </p>
                    </div>
                </div>
                <div class="col-lg-4 col-md-6 mb-4">
                    <div class="feature-card" style="background: rgba(255,255,255,0.1); border: 1px solid rgba(255,255,255,0.2);">
                        <div class="feature-icon" style="background: rgba(255,255,255,0.2);">
                            <i class="fas fa-envelope"></i>
                        </div>
                        <h3 class="feature-title" style="color: white;">邮件支持</h3>
                        <p class="feature-desc" style="color: rgba(255,255,255,0.9);">
                            <strong><EMAIL></strong><br>
                            24小时内回复<br>
                            详细问题描述和解决方案
                        </p>
                    </div>
                </div>
                <div class="col-lg-4 col-md-6 mb-4">
                    <div class="feature-card" style="background: rgba(255,255,255,0.1); border: 1px solid rgba(255,255,255,0.2);">
                        <div class="feature-icon" style="background: rgba(255,255,255,0.2);">
                            <i class="fas fa-comments"></i>
                        </div>
                        <h3 class="feature-title" style="color: white;">在线客服</h3>
                        <p class="feature-desc" style="color: rgba(255,255,255,0.9);">
                            工作日 9:00-18:00<br>
                            即时响应，快速解决<br>
                            专业的服务体验
                        </p>
                    </div>
                </div>
            </div>

            <!-- 网址展示区域 -->
            <div class="row mt-5">
                <div class="col-12">
                    <div class="website-url-footer">
                        <div class="url-footer-content">
                            <div class="url-footer-icon">
                                <i class="fas fa-globe"></i>
                            </div>
                            <div class="url-footer-text">
                                <h4 style="color: white; margin-bottom: 10px;">
                                    <i class="fas fa-bookmark mr-2"></i>记住我们的网址
                                </h4>
                                <div class="url-footer-address">xiaoyuanst.com</div>
                                <p style="color: rgba(255,255,255,0.8); margin: 10px 0 0 0; font-size: 0.9rem;">
                                    智慧食堂平台24小时在线，一键管理，专业服务校园餐全场景
                                </p>
                            </div>
                            <div class="url-footer-copy">
                                <button class="copy-btn-footer" onclick="copyWebsiteUrl()" title="复制网址">
                                    <i class="fas fa-copy"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="row mt-4">
                <div class="col-12">
                    <div style="border-top: 1px solid rgba(255,255,255,0.2); padding-top: 30px;">
                        <div class="row">
            </div>
                            <div class="col-12 text-center">
                                <div style="background: rgba(255,255,255,0.1); padding: 30px; border-radius: 15px;">
                                    <h4 style="color: white; margin-bottom: 20px;">
                                        <i class="fas fa-rocket mr-2"></i>立即开始您的智慧食堂管理之旅
                                    </h4>
                                    <p style="color: rgba(255,255,255,0.9); margin-bottom: 25px;">
                                        无需复杂配置，7个简单步骤即可上手使用<br>
                                        专业的技术支持团队全程为您服务
                                    </p>
                                    <a href="{{ url_for('auth.guest_login') }}" class="btn-hero btn-hero-primary" style="margin-right: 15px;">
                                        <i class="fas fa-play mr-2"></i>免费体验系统
                                    </a>
                                    <a href="{{ url_for('auth.login') }}" class="btn-hero btn-hero-outline">
                                        <i class="fas fa-sign-in-alt mr-2"></i>立即登录
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Bootstrap 4.6.0 JS -->
    <script src="{{ url_for('static', filename='vendor/jquery/jquery.min.js') }}"></script>
    <script src="{{ url_for('static', filename='bootstrap/js/bootstrap.bundle.min.js') }}"></script>
    <!-- 主题切换器 -->
    <script src="{{ url_for('static', filename='js/theme-switcher.js') }}"></script>

    <script>
        // 全局变量
        let allVideos = [];
        let currentPage = 1;
        const videosPerPage = 3; // 每页显示3个视频
        let totalPages = 1;

        // 页面加载完成后执行
        $(document).ready(function() {
            // 导航栏滚动效果
            $(window).scroll(function() {
                if ($(this).scrollTop() > 50) {
                    $('.navbar').addClass('scrolled');
                } else {
                    $('.navbar').removeClass('scrolled');
                }
            });

            // 平滑滚动
            $('a[href^="#"]').on('click', function(event) {
                var target = $(this.getAttribute('href'));
                if (target.length) {
                    event.preventDefault();
                    $('html, body').stop().animate({
                        scrollTop: target.offset().top - 70
                    }, 1000);
                }
            });

            // 加载视频数据
            loadVideos();
        });

        // 加载视频教学内容
        function loadVideos() {
            console.log('开始加载视频...');
            $('#videosLoading').show();
            $('#videosContainer').hide();
            $('#videoPagination').hide();

            $.ajax({
                url: '/api/guide/videos/all',
                method: 'GET',
                success: function(response) {
                    console.log('API响应:', response);
                    $('#videosLoading').hide();

                    if (response.success && response.videos && response.videos.length > 0) {
                        console.log('找到视频数量:', response.videos.length);
                        allVideos = response.videos;
                        totalPages = Math.ceil(allVideos.length / videosPerPage);
                        currentPage = 1;
                        displayCurrentPage();
                        updatePagination();
                        $('#videosContainer').show();
                        $('#videoPagination').show();
                    } else {
                        console.log('没有找到视频或响应失败');
                        displayNoVideos();
                    }
                },
                error: function(xhr, status, error) {
                    console.error('AJAX请求失败:', status, error);
                    console.error('响应状态:', xhr.status);
                    console.error('响应文本:', xhr.responseText);
                    $('#videosLoading').hide();
                    displayNoVideos();
                }
            });
        }

        // 显示当前页的视频
        function displayCurrentPage() {
            const startIndex = (currentPage - 1) * videosPerPage;
            const endIndex = startIndex + videosPerPage;
            const currentVideos = allVideos.slice(startIndex, endIndex);

            // 先隐藏所有视频卡片
            $('.video-card').addClass('hide');

            setTimeout(() => {
                displayVideos(currentVideos);

                // 显示新的视频卡片
                setTimeout(() => {
                    $('.video-card').addClass('show');
                }, 100);
            }, 300);
        }

        // 显示视频列表
        function displayVideos(videos) {
            var html = '';
            videos.forEach(function(video, index) {
                // 构建视频URL - 使用正确的字段名
                const videoUrl = video.url || video.file_path || '';
                const videoTitle = video.name || '未命名视频';
                const videoDesc = video.description || '暂无描述';
                const videoDuration = video.duration || '未知时长';
                const thumbnail = video.thumbnail || '';

                html += `
                    <div class="col-lg-4 col-md-6 mb-4">
                        <div class="video-card" onclick="playVideo('${videoUrl}', '${videoTitle}')">
                            <div class="video-thumbnail">
                                ${thumbnail && thumbnail !== '/static/images/video_thumbnails/default.jpg' ?
                                    `<img src="${thumbnail}" alt="${videoTitle}" style="width: 100%; height: 100%; object-fit: cover; position: absolute; top: 0; left: 0;">
                                     <div style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); color: white; font-size: 3rem; z-index: 2; text-shadow: 0 2px 4px rgba(0,0,0,0.5);">
                                         <i class="fas fa-play-circle"></i>
                                     </div>` :
                                    `<i class="fas fa-play-circle"></i>`
                                }
                            </div>
                            <div class="video-info">
                                <h4 class="video-title">${videoTitle}</h4>
                                <p class="video-desc">${videoDesc}</p>
                                <small class="text-muted">
                                    <i class="fas fa-clock mr-1"></i>
                                    ${videoDuration}
                                    <span class="ml-2">
                                        <i class="fas fa-calendar mr-1"></i>
                                        ${video.created_at ? video.created_at.split(' ')[0] : ''}
                                    </span>
                                </small>
                            </div>
                        </div>
                    </div>
                `;
            });
            $('#videosContainer').html(html);
        }

        // 更新分页信息
        function updatePagination() {
            $('#paginationInfo').text(`第 ${currentPage} 页 / 共 ${totalPages} 页`);

            // 更新按钮状态
            $('#prevBtn').prop('disabled', currentPage <= 1);
            $('#nextBtn').prop('disabled', currentPage >= totalPages);
        }

        // 翻页函数
        function changePage(direction) {
            const newPage = currentPage + direction;

            if (newPage >= 1 && newPage <= totalPages) {
                currentPage = newPage;
                displayCurrentPage();
                updatePagination();

                // 滚动到视频区域
                $('html, body').animate({
                    scrollTop: $('#videos').offset().top - 100
                }, 500);
            }
        }

        // 显示无视频内容
        function displayNoVideos() {
            $('#videosContainer').show();
            $('#videoPagination').hide();

            var html = `
                <div class="col-12 text-center">
                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="fas fa-video"></i>
                        </div>
                        <h3 class="feature-title">视频教学</h3>
                        <p class="feature-desc">
                            视频教学内容正在制作中，敬请期待！<br>
                            您可以先体验系统的各项功能。
                        </p>
                        <a href="{{ url_for('auth.guest_login') }}" class="btn btn-primary btn-lg mt-3">
                            <i class="fas fa-play mr-2"></i>立即体验系统
                        </a>
                    </div>
                </div>
            `;
            $('#videosContainer').html(html);
        }

        // 播放视频
        function playVideo(videoUrl, title) {
            if (!videoUrl) {
                alert('视频链接不可用');
                return;
            }

            // 确保视频URL是完整的
            let fullVideoUrl = videoUrl;
            if (videoUrl.startsWith('/static/')) {
                fullVideoUrl = window.location.origin + videoUrl;
            }

            // 创建模态框播放视频
            var modal = `
                <div class="modal fade" id="videoModal" tabindex="-1" role="dialog">
                    <div class="modal-dialog modal-xl" role="document">
                        <div class="modal-content">
                            <div class="modal-header">
                                <h5 class="modal-title">
                                    <i class="fas fa-play-circle mr-2"></i>${title}
                                </h5>
                                <button type="button" class="close" data-dismiss="modal">
                                    <span>&times;</span>
                                </button>
                            </div>
                            <div class="modal-body p-0">
                                <div class="embed-responsive embed-responsive-16by9">
                                    <video class="embed-responsive-item" controls preload="metadata">
                                        <source src="${fullVideoUrl}" type="video/mp4">
                                        <source src="${fullVideoUrl}" type="video/webm">
                                        <source src="${fullVideoUrl}" type="video/ogg">
                                        您的浏览器不支持视频播放。
                                    </video>
                                </div>
                            </div>
                            <div class="modal-footer">
                                <small class="text-muted">
                                    <i class="fas fa-info-circle mr-1"></i>
                                    如果视频无法播放，请检查网络连接或联系管理员
                                </small>
                            </div>
                        </div>
                    </div>
                </div>
            `;

            // 移除已存在的模态框
            $('#videoModal').remove();

            // 添加新模态框并显示
            $('body').append(modal);
            $('#videoModal').modal('show');

            // 模态框关闭时暂停视频
            $('#videoModal').on('hidden.bs.modal', function() {
                const video = $(this).find('video')[0];
                if (video) {
                    video.pause();
                    video.currentTime = 0;
                }
                $(this).remove();
            });

            // 视频加载错误处理
            $('#videoModal video').on('error', function() {
                $(this).parent().html(`
                    <div class="text-center p-5">
                        <i class="fas fa-exclamation-triangle text-warning" style="font-size: 3rem;"></i>
                        <h5 class="mt-3">视频加载失败</h5>
                        <p class="text-muted">视频文件可能不存在或格式不支持</p>
                        <small class="text-muted">视频路径: ${fullVideoUrl}</small>
                    </div>
                `);
            });
        }

        // 复制网址功能
        function copyWebsiteUrl() {
            const url = 'xiaoyuanst.com';

            // 尝试使用现代的 Clipboard API
            if (navigator.clipboard && window.isSecureContext) {
                navigator.clipboard.writeText(url).then(function() {
                    showCopySuccess();
                }).catch(function(err) {
                    console.error('复制失败:', err);
                    fallbackCopyTextToClipboard(url);
                });
            } else {
                // 回退到传统方法
                fallbackCopyTextToClipboard(url);
            }
        }

        // 传统复制方法（兼容性更好）
        function fallbackCopyTextToClipboard(text) {
            const textArea = document.createElement("textarea");
            textArea.value = text;

            // 避免滚动到底部
            textArea.style.top = "0";
            textArea.style.left = "0";
            textArea.style.position = "fixed";
            textArea.style.opacity = "0";

            document.body.appendChild(textArea);
            textArea.focus();
            textArea.select();

            try {
                const successful = document.execCommand('copy');
                if (successful) {
                    showCopySuccess();
                } else {
                    showCopyError();
                }
            } catch (err) {
                console.error('复制失败:', err);
                showCopyError();
            }

            document.body.removeChild(textArea);
        }

        // 显示复制成功提示
        function showCopySuccess() {
            // 移除已存在的提示
            $('.copy-success').remove();

            // 创建成功提示
            const successDiv = $(`
                <div class="copy-success">
                    <i class="fas fa-check-circle mr-2"></i>
                    网址已复制到剪贴板！
                </div>
            `);

            $('body').append(successDiv);

            // 2秒后自动移除
            setTimeout(() => {
                successDiv.remove();
            }, 2000);
        }

        // 显示复制失败提示
        function showCopyError() {
            // 移除已存在的提示
            $('.copy-success').remove();

            // 创建失败提示
            const errorDiv = $(`
                <div class="copy-success" style="background: rgba(220, 53, 69, 0.95);">
                    <i class="fas fa-exclamation-circle mr-2"></i>
                    复制失败，请手动复制：xiaoyuanst.com
                </div>
            `);

            $('body').append(errorDiv);

            // 3秒后自动移除
            setTimeout(() => {
                errorDiv.remove();
            }, 3000);
        }

        // 页面加载完成后的初始化
        $(document).ready(function() {
            // 网址卡片动画效果
            $('.website-url-card').hover(
                function() {
                    $(this).find('.url-address').css('color', 'var(--theme-primary-dark)');
                },
                function() {
                    $(this).find('.url-address').css('color', 'var(--theme-primary)');
                }
            );

            // 添加键盘快捷键支持（Ctrl+C 复制网址）
            $(document).keydown(function(e) {
                if (e.ctrlKey && e.keyCode === 67 && $('.website-url-card:hover').length > 0) {
                    e.preventDefault();
                    copyWebsiteUrl();
                }
            });
        });
    </script>
{% endblock %}
</body>
</html>