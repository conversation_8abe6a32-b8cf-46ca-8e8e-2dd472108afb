{% extends 'base_landing.html' %}

{% block title %}首页 - 智慧食堂管理平台{% endblock %}

{% block meta %}
    <meta name="description" content="智慧食堂平台提供专业的校园食堂管理解决方案，实现食品安全可视化、可管控、可追溯">
    <meta name="keywords" content="智慧食堂,校园餐饮,食品安全,食堂管理,财务管理">
{% endblock %}

{% block styles %}
<style nonce="{{ csp_nonce }}">
    /* 只保留必要的自定义样式 */
    .hero-section {
        background: linear-gradient(135deg, var(--bs-primary) 0%, #4f46e5 100%);
        min-height: 100vh;
        color: white;
    }

    /* 强制卡片网格布局 - 不依赖Bootstrap */
    .card-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 1.5rem;
    }

    .guide-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 1rem;
    }

    /* Windows桌面优化 */
    @media (min-width: 1200px) {
        .card-grid {
            grid-template-columns: repeat(3, 1fr);
        }
        .guide-grid {
            grid-template-columns: repeat(7, 1fr);
        }
    }
</style>
{% endblock %}

{% block content %}

<!-- Hero区域 -->
<section class="hero-section d-flex align-items-center">
    <div class="container">
        <div class="row align-items-center min-vh-100 py-5">
            <div class="col-lg-6 order-2 order-lg-1">
                <div class="hero-content">
                    <div class="mb-4">
                        <span class="badge bg-light text-primary px-3 py-2 rounded-pill mb-3">
                            <i class="fas fa-star me-2"></i>专业校园餐饮管理
                        </span>
                    </div>
                    <h1 class="display-4 fw-bold mb-4">智慧食堂管理平台</h1>
                    <p class="lead mb-4">专业的校园食堂管理解决方案，实现食品安全可视化、可管控、可追溯，让食堂管理更智能、更高效</p>
                    
                    <div class="hero-features mb-4">
                        <div class="row g-3">
                            <div class="col-md-6">
                                <div class="d-flex align-items-center">
                                    <i class="fas fa-shield-check text-warning me-2"></i>
                                    <span>食品安全追溯</span>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="d-flex align-items-center">
                                    <i class="fas fa-boxes text-warning me-2"></i>
                                    <span>智能库存管理</span>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="d-flex align-items-center">
                                    <i class="fas fa-chart-pie text-warning me-2"></i>
                                    <span>财务成本控制</span>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="d-flex align-items-center">
                                    <i class="fas fa-heartbeat text-warning me-2"></i>
                                    <span>营养健康管理</span>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="d-flex flex-wrap gap-3">
                        <a href="{{ url_for('auth.guest_login') }}" class="btn btn-light btn-lg px-4">
                            <i class="fas fa-rocket me-2"></i>立即体验
                        </a>
                        <a href="#features" class="btn btn-outline-light btn-lg px-4">
                            <i class="fas fa-info-circle me-2"></i>了解更多
                        </a>
                    </div>
                </div>
            </div>
            <div class="col-lg-6 order-1 order-lg-2 mb-5 mb-lg-0">
                <div class="text-center">
                    <i class="fas fa-laptop-code" style="font-size: 10rem; opacity: 0.3;"></i>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- 特色功能区域 -->
<section class="py-5 bg-light" id="features">
    <div class="container">
        <div class="row">
            <div class="col-12 text-center mb-5">
                <h2 class="display-5 fw-bold">核心功能</h2>
                <p class="lead text-muted">全方位的食堂管理解决方案</p>
            </div>
        </div>
        <div class="row g-4">
            <div class="col-lg-4 col-md-6">
                <div class="card h-100 border-0 shadow-sm">
                    <div class="card-body text-center p-4">
                        <div class="bg-primary text-white rounded-circle d-inline-flex align-items-center justify-content-center mb-3" style="width: 4rem; height: 4rem;">
                            <i class="fas fa-shield-alt fs-4"></i>
                        </div>
                        <h5 class="card-title">食品安全管理</h5>
                        <p class="card-text text-muted">全程追溯食品来源，确保食品安全，建立完善的安全管理体系</p>
                    </div>
                </div>
            </div>
            <div class="col-lg-4 col-md-6">
                <div class="card h-100 border-0 shadow-sm">
                    <div class="card-body text-center p-4">
                        <div class="bg-success text-white rounded-circle d-inline-flex align-items-center justify-content-center mb-3" style="width: 4rem; height: 4rem;">
                            <i class="fas fa-chart-line fs-4"></i>
                        </div>
                        <h5 class="card-title">财务管理</h5>
                        <p class="card-text text-muted">专业的财务管理系统，实现成本控制和财务分析</p>
                    </div>
                </div>
            </div>
            <div class="col-lg-4 col-md-6">
                <div class="card h-100 border-0 shadow-sm">
                    <div class="card-body text-center p-4">
                        <div class="bg-info text-white rounded-circle d-inline-flex align-items-center justify-content-center mb-3" style="width: 4rem; height: 4rem;">
                            <i class="fas fa-utensils fs-4"></i>
                        </div>
                        <h5 class="card-title">菜谱管理</h5>
                        <p class="card-text text-muted">科学的菜谱制定和营养搭配，确保膳食营养均衡</p>
                    </div>
                </div>
            </div>
            <div class="col-lg-4 col-md-6">
                <div class="card h-100 border-0 shadow-sm">
                    <div class="card-body text-center p-4">
                        <div class="bg-warning text-white rounded-circle d-inline-flex align-items-center justify-content-center mb-3" style="width: 4rem; height: 4rem;">
                            <i class="fas fa-warehouse fs-4"></i>
                        </div>
                        <h5 class="card-title">库存管理</h5>
                        <p class="card-text text-muted">智能化库存管理，实时监控库存状态，避免浪费</p>
                    </div>
                </div>
            </div>
            <div class="col-lg-4 col-md-6">
                <div class="card h-100 border-0 shadow-sm">
                    <div class="card-body text-center p-4">
                        <div class="bg-danger text-white rounded-circle d-inline-flex align-items-center justify-content-center mb-3" style="width: 4rem; height: 4rem;">
                            <i class="fas fa-truck fs-4"></i>
                        </div>
                        <h5 class="card-title">供应商管理</h5>
                        <p class="card-text text-muted">建立稳定的供应链体系，确保食材质量和供应稳定</p>
                    </div>
                </div>
            </div>
            <div class="col-lg-4 col-md-6">
                <div class="card h-100 border-0 shadow-sm">
                    <div class="card-body text-center p-4">
                        <div class="bg-secondary text-white rounded-circle d-inline-flex align-items-center justify-content-center mb-3" style="width: 4rem; height: 4rem;">
                            <i class="fas fa-mobile-alt fs-4"></i>
                        </div>
                        <h5 class="card-title">移动端支持</h5>
                        <p class="card-text text-muted">全面支持移动设备，随时随地进行食堂管理</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- 新用户入门指南 -->
<section class="py-5">
    <div class="container">
        <div class="row">
            <div class="col-12 text-center mb-5">
                <h2 class="display-5 fw-bold">新用户入门指南</h2>
                <p class="lead text-muted">7步快速上手，轻松开启智慧食堂管理</p>
            </div>
        </div>
        <div class="row g-4">
            <div class="col-xl col-lg-3 col-md-6">
                <div class="card h-100 border-start border-warning border-4">
                    <div class="card-body text-center p-3">
                        <div class="bg-warning text-white rounded-circle d-inline-flex align-items-center justify-content-center mb-3" style="width: 3rem; height: 3rem;">
                            <i class="fas fa-user-plus"></i>
                        </div>
                        <h6 class="card-title">1. 注册登录</h6>
                        <p class="card-text small">使用学校信息注册账号，开启智慧食堂管理之旅</p>
                    </div>
                </div>
            </div>
            <div class="col-xl col-lg-3 col-md-6">
                <div class="card h-100 border-start border-success border-4">
                    <div class="card-body text-center p-3">
                        <div class="bg-success text-white rounded-circle d-inline-flex align-items-center justify-content-center mb-3" style="width: 3rem; height: 3rem;">
                            <i class="fas fa-edit"></i>
                        </div>
                        <h6 class="card-title">2. 完善信息</h6>
                        <p class="card-text small">补充学校基础信息，建立完整的管理档案</p>
                    </div>
                </div>
            </div>
            <div class="col-xl col-lg-3 col-md-6">
                <div class="card h-100 border-start border-info border-4">
                    <div class="card-body text-center p-3">
                        <div class="bg-info text-white rounded-circle d-inline-flex align-items-center justify-content-center mb-3" style="width: 3rem; height: 3rem;">
                            <i class="fas fa-truck"></i>
                        </div>
                        <h6 class="card-title">3. 添加供应商</h6>
                        <p class="card-text small">建立供应商档案，构建稳定的供应链体系</p>
                    </div>
                </div>
            </div>
            <div class="col-xl col-lg-3 col-md-6">
                <div class="card h-100 border-start border-primary border-4">
                    <div class="card-body text-center p-3">
                        <div class="bg-primary text-white rounded-circle d-inline-flex align-items-center justify-content-center mb-3" style="width: 3rem; height: 3rem;">
                            <i class="fas fa-apple-alt"></i>
                        </div>
                        <h6 class="card-title">4. 录入食材</h6>
                        <p class="card-text small">建立食材基础数据库，为菜单制定做准备</p>
                    </div>
                </div>
            </div>
            <div class="col-xl col-lg-3 col-md-6">
                <div class="card h-100 border-start border-danger border-4">
                    <div class="card-body text-center p-3">
                        <div class="bg-danger text-white rounded-circle d-inline-flex align-items-center justify-content-center mb-3" style="width: 3rem; height: 3rem;">
                            <i class="fas fa-clipboard-list"></i>
                        </div>
                        <h6 class="card-title">5. 制定食谱</h6>
                        <p class="card-text small">创建营养均衡的食谱，确保膳食营养搭配合理</p>
                    </div>
                </div>
            </div>
            <div class="col-xl col-lg-3 col-md-6">
                <div class="card h-100 border-start border-secondary border-4">
                    <div class="card-body text-center p-3">
                        <div class="bg-secondary text-white rounded-circle d-inline-flex align-items-center justify-content-center mb-3" style="width: 3rem; height: 3rem;">
                            <i class="fas fa-calendar-week"></i>
                        </div>
                        <h6 class="card-title">6. 安排菜单</h6>
                        <p class="card-text small">制定周菜单计划，科学安排每日膳食</p>
                    </div>
                </div>
            </div>
            <div class="col-xl col-lg-3 col-md-6">
                <div class="card h-100 border-start border-dark border-4">
                    <div class="card-body text-center p-3">
                        <div class="bg-dark text-white rounded-circle d-inline-flex align-items-center justify-content-center mb-3" style="width: 3rem; height: 3rem;">
                            <i class="fas fa-play-circle"></i>
                        </div>
                        <h6 class="card-title">7. 开始使用</h6>
                        <p class="card-text small">进入日常管理流程，享受智慧食堂带来的便利</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

{% endblock %}
