<!DOCTYPE html>
<html lang="zh-CN" data-bs-theme="light">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <meta name="theme-color" content="{{ theme_color|default('primary') }}">
    <title>{% block title %}{{ project_name|default('校园餐智慧食堂(Scmmp) ') }}{% endblock %}</title>

    <!-- 动态Favicon -->
    {% if system_logo %}
    <link rel="icon" type="image/x-icon" href="{{ system_logo }}">
    <link rel="shortcut icon" type="image/x-icon" href="{{ system_logo }}">
    <link rel="apple-touch-icon" href="{{ system_logo }}">
    {% else %}
    <link rel="icon" type="image/x-icon" href="{{ url_for('static', filename='img/favicon.ico') }}">
    <link rel="shortcut icon" type="image/x-icon" href="{{ url_for('static', filename='img/favicon.ico') }}">
    {% endif %}

    <!-- Bootstrap 5.3.6 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.6/dist/css/bootstrap.min.css" rel="stylesheet"
          integrity="sha384-T3c6CoIi6uLrA9TneNEoa7RxnatzjcDSCmG1MXxSR1GAsXEV/Dwwykc2MPK8M2HN" crossorigin="anonymous">

    <!-- Font Awesome 6.5.1 -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css"
          integrity="sha512-DTOQO9RWCH3ppGqcWaEA1BIZOC6xxalwEsw9c2QQeAIftl+Vegovlnee1c9QX4TctnWMn13TZye+giMm8e2LwA==" crossorigin="anonymous">



    {% block styles %}{% endblock %}

    <!-- Bootstrap 5.3.6 核心样式 -->
    <style>
        :root {
            --sidebar-width: 280px;
            --sidebar-collapsed-width: 70px;
            --header-height: 64px;
            --transition-speed: 0.3s;
            --sidebar-bg: #212529;
            --sidebar-text: rgba(255, 255, 255, 0.85);
            --sidebar-hover: rgba(255, 255, 255, 0.1);
            --content-bg: var(--bs-body-bg);
        }

        /* 全局布局 */
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
            background-color: var(--content-bg);
        }

        .app-container {
            min-height: 100vh;
            display: flex;
            flex-direction: column;
        }

        /* 顶部导航栏 - Bootstrap 5.3.6 */
        .top-navbar {
            height: var(--header-height);
            background: var(--bs-body-bg);
            border-bottom: 1px solid var(--bs-border-color);
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            z-index: 1030;
            padding: 0;
        }

        .navbar-brand {
            font-weight: 600;
            font-size: 1.25rem;
            color: var(--bs-body-color);
            text-decoration: none;
            display: flex;
            align-items: center;
            gap: 0.75rem;
        }

        .navbar-brand:hover {
            color: var(--bs-primary);
        }

        .navbar-logo {
            height: 40px;
            max-width: 120px;
            object-fit: contain;
        }

        .navbar-brand-container {
            display: flex;
            flex-direction: column;
            line-height: 1.2;
        }

        .navbar-brand-text {
            font-weight: 600;
            font-size: 1.1rem;
        }

        .navbar-school-name {
            font-size: 0.8rem;
            opacity: 0.7;
            font-weight: 400;
        }

        /* 侧边栏 - Bootstrap 5.3.6 */
        .sidebar {
            width: var(--sidebar-width);
            background: var(--sidebar-bg);
            color: var(--sidebar-text);
            position: fixed;
            top: var(--header-height);
            left: 0;
            height: calc(100vh - var(--header-height));
            z-index: 1020;
            overflow-y: auto;
            transition: all var(--transition-speed) ease;
            border-right: 1px solid var(--bs-border-color);
        }

        .sidebar.collapsed {
            width: var(--sidebar-collapsed-width);
        }

        .sidebar-header {
            padding: 1rem;
            text-align: center;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            font-weight: 600;
        }

        .sidebar-menu {
            padding: 1rem 0;
        }

        .sidebar-menu .nav-item {
            margin: 0.25rem 0.75rem;
        }

        .sidebar-menu .nav-link {
            color: var(--sidebar-text);
            padding: 0.75rem 1rem;
            display: flex;
            align-items: center;
            border-radius: 0.5rem;
            transition: all var(--transition-speed) ease;
            text-decoration: none;
            font-weight: 500;
        }

        .sidebar-menu .nav-link:hover {
            color: #fff;
            background: var(--sidebar-hover);
        }

        .sidebar-menu .nav-link.active {
            background: var(--bs-primary);
            color: #fff;
        }

        .sidebar-menu .nav-link i {
            width: 20px;
            margin-right: 0.75rem;
            text-align: center;
            font-size: 1rem;
        }

        .sidebar.collapsed .nav-link span {
            display: none;
        }

        .sidebar.collapsed .nav-link i {
            margin-right: 0;
        }

        /* 主内容区域 - Bootstrap 5.3.6 */
        .main-wrapper {
            display: flex;
            flex: 1;
            margin-top: var(--header-height);
        }

        .main-content {
            flex: 1;
            margin-left: var(--sidebar-width);
            padding: 2rem;
            transition: margin-left var(--transition-speed) ease;
            background-color: var(--content-bg);
            min-height: calc(100vh - var(--header-height));
        }

        .main-content.expanded {
            margin-left: var(--sidebar-collapsed-width);
        }

        /* 主题切换器 */
        .theme-switcher-panel {
            min-width: 220px;
            padding: 0.5rem;
        }

        .theme-option {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            padding: 0.5rem;
            border-radius: 0.375rem;
            transition: background-color 0.2s ease;
        }

        .theme-option:hover {
            background-color: var(--bs-secondary-bg);
        }

        .theme-preview {
            width: 20px;
            height: 20px;
            border-radius: 50%;
            border: 2px solid var(--bs-border-color);
        }

        /* 响应式设计 - Bootstrap 5.3.6 */
        @media (max-width: 991.98px) {
            .sidebar {
                transform: translateX(-100%);
            }

            .sidebar.show {
                transform: translateX(0);
            }

            .main-content {
                margin-left: 0;
            }

            .main-content.expanded {
                margin-left: 0;
            }

            .navbar-school-name {
                display: none;
            }
        }

        /* 暗色主题适配 */
        [data-bs-theme="dark"] {
            --sidebar-bg: #1a1d20;
            --content-bg: var(--bs-dark);
        }

        [data-bs-theme="dark"] .top-navbar {
            background: var(--bs-dark);
            border-bottom-color: var(--bs-border-color);
        }

        [data-bs-theme="dark"] .sidebar {
            border-right-color: var(--bs-border-color);
        }
    </style>

    <!-- 关键脚本 -->
    <script nonce="{{ csp_nonce }}" src="{{ url_for('static', filename='js/critical-handler-simple.js') }}"></script>
    <script nonce="{{ csp_nonce }}" src="{{ url_for('static', filename='js/comprehensive-event-handler.js') }}"></script>
</head>
<body data-theme="{{ theme_color|default('primary') }}" class="app-container">
    <!-- 顶部导航栏 - Bootstrap 5.3.6 -->
    <nav class="navbar navbar-expand-lg top-navbar">
        <div class="container-fluid">
            <!-- 侧边栏切换按钮 -->
            <button class="btn btn-outline-secondary me-3" type="button" id="sidebarToggle" aria-label="切换侧边栏">
                <i class="fas fa-bars"></i>
            </button>

            <!-- 品牌标识 -->
            <a class="navbar-brand" href="{{ url_for('main.index') }}">
                {% if system_logo %}
                <img src="{{ system_logo }}" alt="{{ project_name }}" class="navbar-logo">
                {% endif %}
                <div class="navbar-brand-container">
                    <span class="navbar-brand-text">{{ project_name|default('校园餐智慧食堂平台') }}</span>
                    {% if current_user.is_authenticated and current_user.get_current_area() %}
                    <span class="navbar-school-name">{{ current_user.get_current_area().name }}</span>
                    {% endif %}
                </div>
            </a>

            <!-- 右侧工具栏 -->
            <div class="navbar-nav ms-auto">
                <!-- 主题切换器 -->
                <div class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle" href="#" id="themeDropdown" role="button"
                       data-bs-toggle="dropdown" aria-expanded="false" title="切换主题 (Ctrl+Alt+T)">
                        <i class="fas fa-palette"></i>
                        <span class="d-none d-md-inline ms-1">主题</span>
                    </a>
                    <ul class="dropdown-menu dropdown-menu-end theme-switcher-panel" aria-labelledby="themeDropdown">
                        <li><h6 class="dropdown-header">🎨 主题选择</h6></li>
                        <li>
                            <a class="dropdown-item theme-option" href="#" data-theme="primary">
                                <span class="theme-preview bg-primary"></span>
                                <span>🌊 海洋蓝主题</span>
                            </a>
                        </li>
                        <li>
                            <a class="dropdown-item theme-option" href="#" data-theme="success">
                                <span class="theme-preview bg-success"></span>
                                <span>🌿 自然绿主题</span>
                            </a>
                        </li>
                        <li>
                            <a class="dropdown-item theme-option" href="#" data-theme="warning">
                                <span class="theme-preview bg-warning"></span>
                                <span>🌅 温暖橙主题</span>
                            </a>
                        </li>
                        <li><hr class="dropdown-divider"></li>
                        <li>
                            <a class="dropdown-item" href="#" onclick="toggleDarkMode()">
                                <i class="fas fa-moon me-2"></i>
                                <span>切换暗色模式</span>
                            </a>
                        </li>
                    </ul>
                </div>

                <!-- 用户菜单 -->
                {% if current_user.is_authenticated %}
                <div class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle" href="#" id="userDropdown" role="button"
                       data-bs-toggle="dropdown" aria-expanded="false">
                        <i class="fas fa-user-circle"></i>
                        <span class="d-none d-md-inline ms-1">{{ current_user.real_name or current_user.username }}</span>
                    </a>
                    <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="userDropdown">
                        <li><h6 class="dropdown-header">用户操作</h6></li>
                        <li><a class="dropdown-item" href="{{ url_for('main.dashboard') }}"><i class="fas fa-tachometer-alt me-2"></i>控制台</a></li>
                        <li><a class="dropdown-item" href="#"><i class="fas fa-user-cog me-2"></i>个人设置</a></li>
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item" href="{{ url_for('auth.logout') }}"><i class="fas fa-sign-out-alt me-2"></i>退出登录</a></li>
                    </ul>
                </div>
                {% else %}
                <div class="nav-item">
                    <a class="nav-link" href="{{ url_for('auth.login') }}">
                        <i class="fas fa-sign-in-alt"></i>
                        <span class="d-none d-md-inline ms-1">登录</span>
                    </a>
                </div>
                {% endif %}
            </div>
        </div>
    </nav>

    <!-- 主体布局 -->
    <div class="main-wrapper">
        <!-- 侧边栏 - Bootstrap 5.3.6 -->
        <nav class="sidebar" id="sidebar">
            <div class="sidebar-header">
                <h6 class="mb-0">导航菜单</h6>
            </div>
            <div class="sidebar-menu">
                {% if current_user.is_authenticated %}
                    {% for menu_item in user_menu %}
                        {% if menu_item.children %}
                            <div class="nav-item">
                                <a class="nav-link {% if menu_item.is_active %}active{% endif %}"
                                   href="#{{ menu_item.id }}Submenu"
                                   data-bs-toggle="collapse"
                                   aria-expanded="{% if menu_item.is_active %}true{% else %}false{% endif %}">
                                    {% if menu_item.icon %}<i class="{{ menu_item.icon }}"></i>{% endif %}
                                    <span>{{ menu_item.name }}</span>
                                    <i class="fas fa-chevron-down ms-auto"></i>
                                </a>
                                <div class="collapse {% if menu_item.is_active %}show{% endif %}"
                                     id="{{ menu_item.id }}Submenu">
                                    <div class="ms-3">
                                        {% for child in menu_item.children %}
                                            {% if child.get('is_header') %}
                                                <div class="nav-header small text-muted px-3 py-1">{{ child.name }}</div>
                                            {% else %}
                                                <a class="nav-link {% if child.is_active %}active{% endif %}"
                                                   href="{{ child.url }}">
                                                    {% if child.icon %}<i class="{{ child.icon }}"></i>{% endif %}
                                                    <span>{{ child.name }}</span>
                                                </a>
                                            {% endif %}
                                        {% endfor %}
                                    </div>
                                </div>
                            </div>
                        {% else %}
                            <div class="nav-item">
                                <a class="nav-link {% if menu_item.is_active %}active{% endif %}"
                                   href="{{ menu_item.url }}">
                                    {% if menu_item.icon %}<i class="{{ menu_item.icon }}"></i>{% endif %}
                                    <span>{{ menu_item.name }}</span>
                                </a>
                            </div>
                        {% endif %}
                    {% endfor %}
                {% endif %}
            </div>
        </nav>

        <!-- 主内容区域 -->
        <main class="main-content" id="mainContent">
            <!-- 面包屑导航 -->
            {% block breadcrumb %}{% endblock %}

            <!-- 页面内容 -->
            {% block content %}{% endblock %}
        </main>
    </div>

    <!-- Bootstrap 5.3.6 核心脚本 -->
    <script src="https://code.jquery.com/jquery-3.7.1.min.js"
            integrity="sha256-/JqT3SQfawRcv/BIHPThkBvs0OEvtFFmqPF/lYI/Cxo=" crossorigin="anonymous"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.6/dist/js/bootstrap.bundle.min.js"
            integrity="sha384-YvpcrYf0tY3lHB60NNkmXc5s9fDVZLESaAA55NDzOxhy9GkcIdslK1eN7N6jIeHz" crossorigin="anonymous"></script>



    <!-- 核心功能脚本 -->
    <script nonce="{{ csp_nonce }}">
        // Bootstrap 5.3.6 全局配置
        document.addEventListener('DOMContentLoaded', function() {
            // 初始化侧边栏
            const sidebarToggle = document.getElementById('sidebarToggle');
            const sidebar = document.getElementById('sidebar');
            const mainContent = document.getElementById('mainContent');

            if (sidebarToggle && sidebar && mainContent) {
                sidebarToggle.addEventListener('click', function() {
                    sidebar.classList.toggle('collapsed');
                    mainContent.classList.toggle('expanded');

                    // 保存状态到localStorage
                    const isCollapsed = sidebar.classList.contains('collapsed');
                    localStorage.setItem('sidebar-collapsed', isCollapsed);
                });

                // 恢复侧边栏状态
                const savedState = localStorage.getItem('sidebar-collapsed');
                if (savedState === 'true') {
                    sidebar.classList.add('collapsed');
                    mainContent.classList.add('expanded');
                }
            }

            // 移动端侧边栏处理
            if (window.innerWidth <= 991.98) {
                if (sidebarToggle && sidebar) {
                    sidebarToggle.addEventListener('click', function() {
                        sidebar.classList.toggle('show');
                    });

                    // 点击外部关闭侧边栏
                    document.addEventListener('click', function(e) {
                        if (!sidebar.contains(e.target) && !sidebarToggle.contains(e.target)) {
                            sidebar.classList.remove('show');
                        }
                    });
                }
            }

            // 初始化工具提示
            const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
            tooltipTriggerList.map(function (tooltipTriggerEl) {
                return new bootstrap.Tooltip(tooltipTriggerEl);
            });

            // 初始化弹出框
            const popoverTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="popover"]'));
            popoverTriggerList.map(function (popoverTriggerEl) {
                return new bootstrap.Popover(popoverTriggerEl);
            });
        });

        // 主题切换功能
        function toggleDarkMode() {
            const html = document.documentElement;
            const currentTheme = html.getAttribute('data-bs-theme');
            const newTheme = currentTheme === 'dark' ? 'light' : 'dark';

            html.setAttribute('data-bs-theme', newTheme);
            localStorage.setItem('theme', newTheme);

            // 显示切换提示
            console.log(`已切换到${newTheme === 'dark' ? '暗色' : '亮色'}模式`);
        }

        // 恢复主题设置
        const savedTheme = localStorage.getItem('theme');
        if (savedTheme) {
            document.documentElement.setAttribute('data-bs-theme', savedTheme);
        }


    </script>

    {% block scripts %}{% endblock %}
</body>
</html>
