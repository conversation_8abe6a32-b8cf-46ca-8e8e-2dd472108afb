<!DOCTYPE html>
<html lang="zh-CN" data-bs-theme="light">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <meta name="theme-color" content="{{ theme_color|default('primary') }}">
    <title>{% block title %}{{ project_name|default('校园餐智慧食堂(Scmmp) ') }}{% endblock %}</title>

    <!-- 动态Favicon -->
    {% if system_logo %}
    <link rel="icon" type="image/x-icon" href="{{ system_logo }}">
    <link rel="shortcut icon" type="image/x-icon" href="{{ system_logo }}">
    <link rel="apple-touch-icon" href="{{ system_logo }}">
    {% else %}
    <link rel="icon" type="image/x-icon" href="{{ url_for('static', filename='img/favicon.ico') }}">
    <link rel="shortcut icon" type="image/x-icon" href="{{ url_for('static', filename='img/favicon.ico') }}">
    {% endif %}

    <!-- Bootstrap 5.3.6 CSS - 纯净版本 (无 jQuery 依赖) -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.6/dist/css/bootstrap.min.css" rel="stylesheet"
          integrity="sha384-T3c6CoIi6uLrA9TneNEoa7RxnatzjcDSCmG1MXxSR1GAsXEV/Dwwykc2MPK8M2HN" crossorigin="anonymous">

    <!-- Font Awesome 6.5.1 - 图标库 -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css"
          integrity="sha512-DTOQO9RWCH3ppGqcWaEA1BIZOC6xxalwEsw9c2QQeAIftl+Vegovlnee1c9QX4TctnWMn13TZye+giMm8e2LwA==" crossorigin="anonymous">
    

    
    {% block styles %}{% endblock %}
</head>
<body data-theme="{{ theme_color|default('primary') }}" class="app-container">
    <!-- 顶部导航栏 - Bootstrap 5.3.6 -->
    <nav class="navbar navbar-expand-lg top-navbar">
        <div class="container-fluid">
            <!-- 侧边栏切换按钮 -->
            <button class="btn btn-outline-secondary me-3" type="button" id="sidebarToggle" aria-label="切换侧边栏">
                <i class="fas fa-bars"></i>
            </button>
            
            <!-- 品牌标识 -->
            <a class="navbar-brand" href="{{ url_for('main.index') }}">
                {% if system_logo %}
                <img src="{{ system_logo }}" alt="{{ project_name }}" class="navbar-logo">
                {% endif %}
                <div class="navbar-brand-container">
                    <span class="navbar-brand-text">{{ project_name|default('校园餐智慧食堂平台') }}</span>
                    {% if current_user.is_authenticated and current_user.get_current_area() %}
                    <span class="navbar-school-name">{{ current_user.get_current_area().name }}</span>
                    {% endif %}
                </div>
            </a>
            
            <!-- 右侧工具栏 -->
            <div class="navbar-nav ms-auto">
                <!-- 通知中心 -->
                {% if current_user.is_authenticated %}
                <div class="nav-item dropdown me-2">
                    <a class="nav-link position-relative" href="#" id="notificationDropdown" role="button" 
                       data-bs-toggle="dropdown" aria-expanded="false" title="通知中心">
                        <i class="fas fa-bell"></i>
                        <span class="position-absolute top-0 start-100 translate-middle badge rounded-pill bg-danger">
                            3
                            <span class="visually-hidden">未读通知</span>
                        </span>
                    </a>
                    <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="notificationDropdown" style="min-width: 300px;">
                        <li><h6 class="dropdown-header">最新通知</h6></li>
                        <li><a class="dropdown-item" href="#"><i class="fas fa-info-circle text-info me-2"></i>系统维护通知</a></li>
                        <li><a class="dropdown-item" href="#"><i class="fas fa-exclamation-triangle text-warning me-2"></i>库存预警</a></li>
                        <li><a class="dropdown-item" href="#"><i class="fas fa-check-circle text-success me-2"></i>任务完成</a></li>
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item text-center" href="#">查看所有通知</a></li>
                    </ul>
                </div>
                {% endif %}
                
                <!-- 主题切换器 -->
                <div class="nav-item dropdown me-2">
                    <a class="nav-link dropdown-toggle" href="#" id="themeDropdown" role="button" 
                       data-bs-toggle="dropdown" aria-expanded="false" title="切换主题 (Ctrl+Alt+T)">
                        <i class="fas fa-palette"></i>
                        <span class="d-none d-md-inline ms-1">主题</span>
                    </a>
                    <ul class="dropdown-menu dropdown-menu-end theme-switcher-panel" aria-labelledby="themeDropdown">
                        <!-- 主题选项将由JavaScript动态生成 -->
                    </ul>
                </div>
                
                <!-- 用户菜单 -->
                {% if current_user.is_authenticated %}
                <div class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle" href="#" id="userDropdown" role="button" 
                       data-bs-toggle="dropdown" aria-expanded="false">
                        <i class="fas fa-user-circle"></i>
                        <span class="d-none d-md-inline ms-1">{{ current_user.real_name or current_user.username }}</span>
                    </a>
                    <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="userDropdown">
                        <li><h6 class="dropdown-header">{{ current_user.real_name or current_user.username }}</h6></li>
                        <li><a class="dropdown-item" href="{{ url_for('main.dashboard') }}"><i class="fas fa-tachometer-alt me-2"></i>控制台</a></li>
                        <li><a class="dropdown-item" href="#"><i class="fas fa-user-cog me-2"></i>个人设置</a></li>
                        <li><a class="dropdown-item" href="#"><i class="fas fa-key me-2"></i>修改密码</a></li>
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item" href="{{ url_for('auth.logout') }}"><i class="fas fa-sign-out-alt me-2"></i>退出登录</a></li>
                    </ul>
                </div>
                {% else %}
                <div class="nav-item">
                    <a class="nav-link" href="{{ url_for('auth.login') }}">
                        <i class="fas fa-sign-in-alt"></i>
                        <span class="d-none d-md-inline ms-1">登录</span>
                    </a>
                </div>
                {% endif %}
            </div>
        </div>
    </nav>

    <!-- 主体布局 -->
    <div class="main-wrapper">
        <!-- 侧边栏 - Bootstrap 5.3.6 -->
        <nav class="sidebar" id="sidebar">
            <div class="sidebar-header">
                <h6 class="mb-0">导航菜单</h6>
            </div>
            <div class="sidebar-menu">
                {% if current_user.is_authenticated %}
                    {% for menu_item in user_menu %}
                        {% if menu_item.children %}
                            <div class="nav-item">
                                <a class="nav-link {% if menu_item.is_active %}active{% endif %}" 
                                   href="#{{ menu_item.id }}Submenu" 
                                   data-bs-toggle="collapse"
                                   aria-expanded="{% if menu_item.is_active %}true{% else %}false{% endif %}">
                                    {% if menu_item.icon %}<i class="{{ menu_item.icon }}"></i>{% endif %}
                                    <span>{{ menu_item.name }}</span>
                                    <i class="fas fa-chevron-down ms-auto"></i>
                                </a>
                                <div class="collapse {% if menu_item.is_active %}show{% endif %}" 
                                     id="{{ menu_item.id }}Submenu">
                                    <div class="ms-3">
                                        {% for child in menu_item.children %}
                                            {% if child.get('is_header') %}
                                                <div class="nav-header small text-muted px-3 py-1">{{ child.name }}</div>
                                            {% else %}
                                                <a class="nav-link {% if child.is_active %}active{% endif %}" 
                                                   href="{{ child.url }}">
                                                    {% if child.icon %}<i class="{{ child.icon }}"></i>{% endif %}
                                                    <span>{{ child.name }}</span>
                                                </a>
                                            {% endif %}
                                        {% endfor %}
                                    </div>
                                </div>
                            </div>
                        {% else %}
                            <div class="nav-item">
                                <a class="nav-link {% if menu_item.is_active %}active{% endif %}" 
                                   href="{{ menu_item.url }}">
                                    {% if menu_item.icon %}<i class="{{ menu_item.icon }}"></i>{% endif %}
                                    <span>{{ menu_item.name }}</span>
                                </a>
                            </div>
                        {% endif %}
                    {% endfor %}
                {% endif %}
            </div>
        </nav>

        <!-- 主内容区域 -->
        <main class="main-content" id="mainContent">
            <!-- 面包屑导航 -->
            {% block breadcrumb %}{% endblock %}
            
            <!-- 页面内容 -->
            {% block content %}{% endblock %}
        </main>
    </div>

    <!-- Bootstrap 5.3.6 核心脚本 (无需 jQuery) -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.6/dist/js/bootstrap.bundle.min.js"
            integrity="sha384-YvpcrYf0tY3lHB60NNkmXc5s9fDVZLESaAA55NDzOxhy9GkcIdslK1eN7N6jIeHz" crossorigin="anonymous"></script>
    
    {% block scripts %}{% endblock %}
</body>
</html>
