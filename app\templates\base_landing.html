<!DOCTYPE html>
<html lang="zh-CN" data-bs-theme="light">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <title>{% block title %}{{ project_name|default('校园餐智慧食堂平台') }}{% endblock %}</title>

    {% block meta %}{% endblock %}

    <!-- 动态Favicon -->
    {% if system_logo %}
    <link rel="icon" type="image/x-icon" href="{{ system_logo }}">
    <link rel="shortcut icon" type="image/x-icon" href="{{ system_logo }}">
    <link rel="apple-touch-icon" href="{{ system_logo }}">
    {% else %}
    <link rel="icon" type="image/x-icon" href="{{ url_for('static', filename='img/favicon.ico') }}">
    <link rel="shortcut icon" type="image/x-icon" href="{{ url_for('static', filename='img/favicon.ico') }}">
    {% endif %}

    <!-- 1. Bootstrap 5.3.6 CSS - 基础框架 -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.6/dist/css/bootstrap.min.css" rel="stylesheet"
          integrity="sha384-T3c6CoIi6uLrA9TneNEoa7RxnatzjcDSCmG1MXxSR1GAsXEV/Dwwykc2MPK8M2HN" crossorigin="anonymous">

    <!-- 2. Font Awesome 6.5.1 - 图标库 -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css"
          integrity="sha512-DTOQO9RWCH3ppGqcWaEA1BIZOC6xxalwEsw9c2QQeAIftl+Vegovlnee1c9QX4TctnWMn13TZye+giMm8e2LwA==" crossorigin="anonymous">

    <!-- 3. 第三方插件CSS -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/toastr.js/2.1.4/toastr.min.css"
          integrity="sha512-6S2HWzVFxruDlZxI3sXOZZ4/eJ8AcxkQH1+JjSe/ONCEqR9L4Ysq5JdT5ipqtzU7WHalNwzwBv+iE51gNHJNqQ==" crossorigin="anonymous">

    <!-- 4. 本地基础样式 - 临时禁用测试 -->
    <!-- <link rel="stylesheet" href="{{ url_for('static', filename='css/local-fonts.css') }}?v=1.0.0"> -->
    <!-- <link rel="stylesheet" href="{{ url_for('static', filename='css/bootstrap5-theme.css') }}?v=1.0.0"> -->
    <!-- <link rel="stylesheet" href="{{ url_for('static', filename='css/theme-colors.css') }}?v=2.4.0"> -->
    <!-- <link rel="stylesheet" href="{{ url_for('static', filename='css/components-bs5.css') }}?v=1.0.0"> -->
    <!-- <link rel="stylesheet" href="{{ url_for('static', filename='css/mobile-responsive.css') }}?v=1.0.0"> -->

    <!-- 5. Landing页面基础样式 - 最高优先级 -->
    <style nonce="{{ csp_nonce }}">
        /* Bootstrap 5.3.6 Landing页面基础变量 */
        :root {
            /* 基础颜色变量 - 确保Bootstrap变量可用 */
            --bs-primary-rgb: 13, 110, 253;
            --bs-success-rgb: 25, 135, 84;
            --bs-danger-rgb: 220, 53, 69;
            --bs-warning-rgb: 255, 193, 7;
            --bs-info-rgb: 13, 202, 240;

            /* Landing页面专用变量 */
            --hero-gradient: linear-gradient(135deg, var(--bs-primary) 0%, #4f46e5 50%, var(--bs-primary) 100%);
            --card-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            --card-hover-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
            --section-padding: 5rem 0;
            --border-radius-lg: 1.5rem;
            --transition-smooth: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
        }

        /* 深色主题适配 */
        [data-bs-theme="dark"] {
            --hero-gradient: linear-gradient(135deg, #1e40af 0%, #3730a3 50%, #1e3a8a 100%);
            --card-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
            --card-hover-shadow: 0 20px 40px rgba(0, 0, 0, 0.4);
        }

        body.landing-page {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
            line-height: 1.6;
            overflow-x: hidden;
        }

        /* 导航栏样式 */
        .landing-navbar {
            background: var(--hero-gradient) !important;
            backdrop-filter: blur(10px);
            box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
            padding: 1rem 0;
        }

        .navbar-brand-container {
            display: flex;
            flex-direction: column;
            align-items: flex-start;
        }

        .navbar-brand-text {
            font-weight: 700;
            font-size: 1.5rem;
            color: white !important;
            text-shadow: 0 2px 4px rgba(0,0,0,0.3);
        }

        .navbar-brand-subtitle {
            font-size: 0.7rem;
            font-weight: 400;
            opacity: 0.8;
            margin-top: -2px;
            color: rgba(255,255,255,0.8) !important;
        }

        .navbar-nav .nav-link {
            color: rgba(255,255,255,0.9) !important;
            font-weight: 500;
            margin: 0 10px;
            transition: all 0.3s ease;
            position: relative;
        }

        .navbar-nav .nav-link:hover {
            color: white !important;
            transform: translateY(-2px);
        }

        /* 下拉菜单样式 */
        .dropdown-menu {
            background: white;
            border: none;
            border-radius: 10px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            margin-top: 10px;
        }

        .dropdown-item {
            padding: 12px 20px;
            color: #333;
            transition: all 0.3s ease;
        }

        .dropdown-item:hover {
            background: var(--hero-gradient);
            color: white;
            transform: translateX(5px);
        }

        /* 主要内容区域 */
        .landing-main {
            margin-top: 76px; /* 导航栏高度 */
        }

        /* 动画效果 */
        @keyframes fadeInUp {
            from { opacity: 0; transform: translateY(30px); }
            to { opacity: 1; transform: translateY(0); }
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-10px); }
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .navbar-brand-text {
                font-size: 1.2rem;
            }

            .navbar-brand-subtitle {
                font-size: 0.6rem;
            }
        }
    </style>

    <!-- 6. 页面特定样式 - 最后载入，确保最高优先级 -->
    {% block styles %}{% endblock %}
</head>
<body data-theme="{{ theme_color|default('primary') }}" class="landing-page">

    <!-- 顶部导航栏 - Bootstrap 5.3.6 -->
    <nav class="navbar navbar-expand-lg navbar-dark fixed-top landing-navbar">
        <div class="container">
            <!-- 品牌标识 -->
            <a class="navbar-brand" href="{{ url_for('main.index') }}">
                {% if system_logo %}
                <img src="{{ system_logo }}" alt="{{ project_name }}" class="navbar-logo me-2" style="height: 40px;">
                {% endif %}
                <div class="navbar-brand-container">
                    <span class="navbar-brand-text">{{ project_name|default('智慧食堂平台') }}</span>
                    <small class="navbar-brand-subtitle">xiaoyuanst.com</small>
                </div>
            </a>

            <!-- 移动端切换按钮 -->
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav"
                    aria-controls="navbarNav" aria-expanded="false" aria-label="切换导航">
                <span class="navbar-toggler-icon"></span>
            </button>

            <!-- 导航菜单 -->
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="#home">首页</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#features">核心功能</a>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="guideDropdown" role="button"
                           data-bs-toggle="dropdown" aria-expanded="false">
                            使用指南
                        </a>
                        <ul class="dropdown-menu" aria-labelledby="guideDropdown">
                            <li><a class="dropdown-item" href="#guide">
                                <i class="fas fa-rocket me-2"></i>新用户入门
                            </a></li>
                            <li><a class="dropdown-item" href="#workflow">
                                <i class="fas fa-tasks me-2"></i>日常操作流程
                            </a></li>
                            <li><a class="dropdown-item" href="#financial-guide">
                                <i class="fas fa-calculator me-2"></i>财务管理指南
                            </a></li>
                        </ul>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#videos">视频教学</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('auth.login') }}">登录</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link btn btn-outline-light ms-2 px-3" href="{{ url_for('auth.guest_login') }}"
                           title="无需注册，直接体验">
                            <i class="fas fa-user-friends me-1"></i>体验系统
                        </a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- 主要内容区域 -->
    <main class="landing-main">
        {% block content %}{% endblock %}
    </main>

    <!-- Bootstrap 5.3.6 核心脚本 -->
    <script src="https://code.jquery.com/jquery-3.7.1.min.js"
            integrity="sha256-/JqT3SQfawRcv/BIHPThkBvs0OEvtFFmqPF/lYI/Cxo=" crossorigin="anonymous"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.6/dist/js/bootstrap.bundle.min.js"
            integrity="sha384-YvpcrYf0tY3lHB60NNkmXc5s9fDVZLESaAA55NDzOxhy9GkcIdslK1eN7N6jIeHz" crossorigin="anonymous"></script>

    <!-- 插件脚本 -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/toastr.js/2.1.4/toastr.min.js"
            integrity="sha512-lbwH47l/tPXJYG9AcFNoJaTMhGvYWhVM9YI43CT+uteTRRaiLCui8snIgyAN8XWgNjNhCqlAUdzZptso6OCoFQ==" crossorigin="anonymous"></script>

    <!-- 本地脚本 -->
    <script src="{{ url_for('static', filename='js/bootstrap5-theme.js') }}?v=1.0.0"></script>
    <script src="{{ url_for('static', filename='js/global-bs5.js') }}?v=1.0.0"></script>

    {% block scripts %}
    <script>
        // 添加滚动动画
        document.addEventListener('DOMContentLoaded', function() {
            const featureCards = document.querySelectorAll('.feature-card');
            
            const observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        entry.target.style.opacity = '1';
                    }
                });
            });

            featureCards.forEach(card => {
                observer.observe(card);
            });
        });
    </script>
    {% endblock %}
</body>
</html>
